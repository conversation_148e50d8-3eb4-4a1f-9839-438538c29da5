import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'
import { CreateProjectRequest, UpdateProjectRequest } from '@/types/page-builder'

// GET - جلب جميع مشاريع الصفحات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeUnpublished = searchParams.get('include_unpublished') === 'true'
    const createdBy = searchParams.get('created_by')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const sortBy = searchParams.get('sort_by') || 'updated_at'
    const sortOrder = searchParams.get('sort_order') || 'desc'

    // جلب البيانات الوهمية
    let projects = MockDataManager.getPageProjects()

    // تطبيق الفلاتر
    if (!includeUnpublished) {
      projects = projects.filter(project => project.isPublished)
    }

    if (createdBy) {
      projects = projects.filter(project => project.createdBy === createdBy)
    }

    // الترتيب
    projects.sort((a, b) => {
      const aValue = a[sortBy as keyof typeof a] as string
      const bValue = b[sortBy as keyof typeof b] as string
      
      if (sortOrder === 'desc') {
        return new Date(bValue).getTime() - new Date(aValue).getTime()
      } else {
        return new Date(aValue).getTime() - new Date(bValue).getTime()
      }
    })

    // تطبيق التصفح
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedProjects = projects.slice(startIndex, endIndex)

    // إحصائيات إضافية
    const stats = {
      total: projects.length,
      published: projects.filter(p => p.isPublished).length,
      unpublished: projects.filter(p => !p.isPublished).length,
      byGenerationMode: projects.reduce((acc, project) => {
        acc[project.generationMode] = (acc[project.generationMode] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      byLanguage: projects.reduce((acc, project) => {
        acc[project.settings.language] = (acc[project.settings.language] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    }

    return NextResponse.json({
      projects: paginatedProjects,
      total: projects.length,
      page,
      limit,
      totalPages: Math.ceil(projects.length / limit),
      stats
    })

  } catch (error) {
    console.error('Error fetching page projects:', error)
    return NextResponse.json(
      { error: 'خطأ في جلب مشاريع الصفحات' },
      { status: 500 }
    )
  }
}

// POST - إنشاء مشروع صفحة جديد
export async function POST(request: NextRequest) {
  try {
    const body: CreateProjectRequest = await request.json()
    const {
      name,
      description,
      templateId,
      generationMode,
      settings
    } = body

    // التحقق من البيانات المطلوبة
    if (!name || !generationMode) {
      return NextResponse.json(
        { error: 'اسم المشروع وطريقة الإنشاء مطلوبان' },
        { status: 400 }
      )
    }

    // جلب المشاريع الحالية
    const projects = MockDataManager.getPageProjects()

    // التحقق من عدم تكرار الاسم
    const existingProject = projects.find(project => 
      project.name.toLowerCase() === name.toLowerCase()
    )
    if (existingProject) {
      return NextResponse.json(
        { error: 'مشروع بنفس الاسم موجود بالفعل' },
        { status: 400 }
      )
    }

    // جلب القالب إذا تم تحديده
    let templateComponents = []
    if (templateId) {
      const templates = MockDataManager.getPageTemplates()
      const template = templates.find(t => t.id === templateId)
      if (template) {
        templateComponents = template.components
      }
    }

    // إنشاء المشروع الجديد
    const newProject = {
      id: MockDataManager.generateId(),
      name,
      description: description || '',
      components: templateComponents,
      templateId,
      generationMode,
      settings: {
        title: name,
        description: description || '',
        keywords: [],
        language: 'ar' as const,
        direction: 'rtl' as const,
        ...settings
      },
      isPublished: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'admin-1', // TODO: استخدام معرف المستخدم الحقيقي
      version: 1
    }

    // حفظ المشروع
    projects.push(newProject)
    MockDataManager.savePageProjects(projects)

    return NextResponse.json({ 
      message: 'تم إنشاء المشروع بنجاح',
      project: newProject 
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating page project:', error)
    return NextResponse.json(
      { error: 'خطأ في إنشاء المشروع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث إعدادات عامة للمشاريع
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, projectIds, data } = body

    if (!action || !projectIds || !Array.isArray(projectIds)) {
      return NextResponse.json(
        { error: 'الإجراء ومعرفات المشاريع مطلوبة' },
        { status: 400 }
      )
    }

    const projects = MockDataManager.getPageProjects()
    let updatedCount = 0

    for (const projectId of projectIds) {
      const projectIndex = projects.findIndex(p => p.id === projectId)
      if (projectIndex === -1) continue

      const project = projects[projectIndex]

      switch (action) {
        case 'publish':
          project.isPublished = true
          project.publishedUrl = `https://example.com/pages/${project.id}`
          break
        case 'unpublish':
          project.isPublished = false
          project.publishedUrl = undefined
          break
        case 'duplicate':
          const duplicatedProject = {
            ...project,
            id: MockDataManager.generateId(),
            name: `${project.name} - نسخة`,
            isPublished: false,
            publishedUrl: undefined,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            version: 1
          }
          projects.push(duplicatedProject)
          break
        case 'update_settings':
          if (data) {
            project.settings = { ...project.settings, ...data }
          }
          break
        default:
          continue
      }

      project.updatedAt = new Date().toISOString()
      if (action !== 'duplicate') {
        projects[projectIndex] = project
      }
      updatedCount++
    }

    // حفظ التغييرات
    MockDataManager.savePageProjects(projects)

    return NextResponse.json({
      message: `تم تحديث ${updatedCount} مشروع بنجاح`,
      updatedCount
    })

  } catch (error) {
    console.error('Error updating page projects:', error)
    return NextResponse.json(
      { error: 'خطأ في تحديث المشاريع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مشاريع متعددة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectIds = searchParams.get('ids')?.split(',') || []

    if (projectIds.length === 0) {
      return NextResponse.json(
        { error: 'معرفات المشاريع مطلوبة' },
        { status: 400 }
      )
    }

    const projects = MockDataManager.getPageProjects()
    let deletedCount = 0

    // التحقق من المشاريع المنشورة
    const publishedProjects = projects.filter(project => 
      projectIds.includes(project.id) && project.isPublished
    )

    if (publishedProjects.length > 0) {
      return NextResponse.json(
        { 
          error: 'لا يمكن حذف المشاريع المنشورة. يرجى إلغاء نشرها أولاً.',
          publishedProjects: publishedProjects.map(p => ({ id: p.id, name: p.name }))
        },
        { status: 400 }
      )
    }

    // حذف المشاريع
    const remainingProjects = projects.filter(project => {
      if (projectIds.includes(project.id)) {
        deletedCount++
        return false
      }
      return true
    })

    // حفظ التغييرات
    MockDataManager.savePageProjects(remainingProjects)

    return NextResponse.json({
      message: `تم حذف ${deletedCount} مشروع بنجاح`,
      deletedCount
    })

  } catch (error) {
    console.error('Error deleting page projects:', error)
    return NextResponse.json(
      { error: 'خطأ في حذف المشاريع' },
      { status: 500 }
    )
  }
}
