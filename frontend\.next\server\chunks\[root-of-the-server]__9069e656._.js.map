{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/mockData.ts"], "sourcesContent": ["// بيانات وهمية للتطوير والاختبار\nimport { AIModel, AISubModel, ModelActivity } from '@/types/ai-models'\nimport { PageTemplate, PageProject, ComponentLibraryItem, PageComponent } from '@/types/page-builder'\n\nexport interface MockPage {\n  id: string\n  slug: string\n  is_published: boolean\n  author_id: string\n  featured_image?: string\n  created_at: string\n  updated_at: string\n  page_content: MockPageContent[]\n  profiles?: {\n    full_name: string\n  }\n}\n\nexport interface MockPageContent {\n  id: string\n  page_id: string\n  language: 'ar' | 'en' | 'fr'\n  title: string\n  content: string\n  meta_description?: string\n  meta_keywords?: string\n}\n\nexport interface MockMenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockCategory {\n  id: string\n  name_ar: string\n  name_en?: string\n  name_fr?: string\n  slug: string\n  icon?: string\n  description?: string\n  is_active: boolean\n  order_index: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockProduct {\n  id: string\n  name: string\n  description: string\n  category: string // تغيير من union type إلى string للمرونة\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  created_at: string\n  updated_at: string\n  rating?: number\n  reviews_count?: number\n  features?: string[]\n  specifications?: Record<string, any>\n}\n\nexport interface MockSchool {\n  id: string\n  admin_id?: string\n  name: string\n  name_en?: string\n  name_fr?: string\n  address?: string\n  city?: string\n  phone?: string\n  email?: string\n  website?: string\n  logo_url?: string\n  graduation_date?: string\n  student_count: number\n  is_active: boolean\n  settings?: Record<string, any>\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockOrder {\n  id: string\n  order_number: string\n  customer_id: string\n  customer_name: string\n  customer_email: string\n  customer_phone?: string\n  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'\n  items: MockOrderItem[]\n  subtotal: number\n  tax: number\n  shipping_cost: number\n  total: number\n  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'\n  payment_method?: string\n  shipping_address: {\n    street: string\n    city: string\n    state: string\n    postal_code: string\n    country: string\n  }\n  tracking_number?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  delivery_date?: string\n  school_id?: string\n  school_name?: string\n}\n\nexport interface MockOrderItem {\n  id: string\n  order_id: string\n  product_id: string\n  product_name: string\n  product_image: string\n  category: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  customizations?: {\n    color?: string\n    size?: string\n    embroidery?: string\n    special_requests?: string\n  }\n}\n\n// بيانات وهمية للصفحات\nexport const mockPages: MockPage[] = [\n  {\n    id: '1',\n    slug: 'about-us',\n    is_published: true,\n    author_id: 'admin-1',\n    featured_image: '/images/about-hero.jpg',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '1-ar',\n        page_id: '1',\n        language: 'ar',\n        title: 'من نحن',\n        content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',\n        meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',\n        meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'\n      },\n      {\n        id: '1-en',\n        page_id: '1',\n        language: 'en',\n        title: 'About Us',\n        content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',\n        meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',\n        meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'\n      }\n    ]\n  },\n  {\n    id: '2',\n    slug: 'services',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '2-ar',\n        page_id: '2',\n        language: 'ar',\n        title: 'خدماتنا',\n        content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',\n        meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',\n        meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'\n      }\n    ]\n  },\n  {\n    id: '3',\n    slug: 'contact',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-17T11:00:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '3-ar',\n        page_id: '3',\n        language: 'ar',\n        title: 'اتصل بنا',\n        content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',\n        meta_description: 'تواصل مع منصة أزياء التخرج المغربية',\n        meta_keywords: 'اتصال، تواصل، خدمة العملاء'\n      }\n    ]\n  }\n]\n\n// بيانات وهمية للقوائم\nexport const mockMenuItems: MockMenuItem[] = [\n  {\n    id: '1',\n    title_ar: 'الرئيسية',\n    title_en: 'Home',\n    title_fr: 'Accueil',\n    slug: 'home',\n    icon: 'Home',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    title_ar: 'من نحن',\n    title_en: 'About Us',\n    title_fr: 'À propos',\n    slug: 'about',\n    icon: 'Info',\n    order_index: 2,\n    is_active: true,\n    target_type: 'page',\n    target_value: '1',\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    title_ar: 'خدماتنا',\n    title_en: 'Services',\n    title_fr: 'Services',\n    slug: 'services',\n    icon: 'Settings',\n    order_index: 3,\n    is_active: true,\n    target_type: 'page',\n    target_value: '2',\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    title_ar: 'المنتجات',\n    title_en: 'Products',\n    title_fr: 'Produits',\n    slug: 'products',\n    icon: 'Package',\n    order_index: 4,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products',\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    title_ar: 'تأجير الأزياء',\n    title_en: 'Rental',\n    title_fr: 'Location',\n    slug: 'rental',\n    parent_id: '4',\n    icon: 'Calendar',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=rental',\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  },\n  {\n    id: '6',\n    title_ar: 'بيع الأزياء',\n    title_en: 'Sales',\n    title_fr: 'Vente',\n    slug: 'sales',\n    parent_id: '4',\n    icon: 'ShoppingCart',\n    order_index: 2,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=sale',\n    created_at: '2024-01-15T10:25:00Z',\n    updated_at: '2024-01-15T10:25:00Z'\n  },\n  {\n    id: '7',\n    title_ar: 'الكتالوج',\n    title_en: 'Catalog',\n    title_fr: 'Catalogue',\n    slug: 'catalog',\n    icon: 'Grid3X3',\n    order_index: 5,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/catalog',\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:30:00Z'\n  },\n  {\n    id: '8',\n    title_ar: 'اتصل بنا',\n    title_en: 'Contact',\n    title_fr: 'Contact',\n    slug: 'contact',\n    icon: 'Phone',\n    order_index: 6,\n    is_active: true,\n    target_type: 'page',\n    target_value: '3',\n    created_at: '2024-01-15T10:35:00Z',\n    updated_at: '2024-01-15T10:35:00Z'\n  }\n]\n\n// بيانات وهمية للفئات\nexport const mockCategories: MockCategory[] = [\n  {\n    id: '1',\n    name_ar: 'أثواب التخرج',\n    name_en: 'Graduation Gowns',\n    name_fr: 'Robes de Graduation',\n    slug: 'gown',\n    icon: '👘',\n    description: 'أثواب التخرج الأكاديمية التقليدية',\n    is_active: true,\n    order_index: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    name_ar: 'قبعات التخرج',\n    name_en: 'Graduation Caps',\n    name_fr: 'Chapeaux de Graduation',\n    slug: 'cap',\n    icon: '🎩',\n    description: 'قبعات التخرج الأكاديمية',\n    is_active: true,\n    order_index: 2,\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    name_ar: 'شرابات التخرج',\n    name_en: 'Graduation Tassels',\n    name_fr: 'Glands de Graduation',\n    slug: 'tassel',\n    icon: '🏷️',\n    description: 'شرابات التخرج الملونة',\n    is_active: true,\n    order_index: 3,\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    name_ar: 'أوشحة التخرج',\n    name_en: 'Graduation Stoles',\n    name_fr: 'Étoles de Graduation',\n    slug: 'stole',\n    icon: '🧣',\n    description: 'أوشحة التخرج المميزة',\n    is_active: true,\n    order_index: 4,\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    name_ar: 'القلانس الأكاديمية',\n    name_en: 'Academic Hoods',\n    name_fr: 'Capuches Académiques',\n    slug: 'hood',\n    icon: '🎓',\n    description: 'القلانس الأكاديمية للدرجات العليا',\n    is_active: true,\n    order_index: 5,\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  }\n]\n\n// بيانات وهمية للمنتجات\nexport const mockProducts: MockProduct[] = [\n  {\n    id: '1',\n    name: 'ثوب التخرج الكلاسيكي',\n    description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',\n    category: 'gown',\n    price: 299.99,\n    rental_price: 99.99,\n    colors: ['أسود', 'أزرق داكن', 'بورجوندي'],\n    sizes: ['S', 'M', 'L', 'XL', 'XXL'],\n    images: ['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],\n    stock_quantity: 25,\n    is_available: true,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    rating: 4.8,\n    reviews_count: 42,\n    features: ['مقاوم للتجاعيد', 'قابل للغسيل', 'خامة عالية الجودة'],\n    specifications: {\n      material: 'بوليستر عالي الجودة',\n      weight: '0.8 كيلو',\n      care: 'غسيل جاف أو غسيل عادي'\n    }\n  },\n  {\n    id: '2',\n    name: 'قبعة التخرج التقليدية',\n    description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',\n    category: 'cap',\n    price: 79.99,\n    rental_price: 29.99,\n    colors: ['أسود', 'أزرق داكن'],\n    sizes: ['One Size'],\n    images: ['/images/products/cap-traditional-1.jpg'],\n    stock_quantity: 50,\n    is_available: true,\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    rating: 4.6,\n    reviews_count: 28,\n    features: ['مقاس واحد يناسب الجميع', 'شرابة ذهبية', 'تصميم تقليدي'],\n    specifications: {\n      material: 'قطن مخلوط',\n      tassel_color: 'ذهبي',\n      adjustable: 'نعم'\n    }\n  },\n  {\n    id: '3',\n    name: 'وشاح التخرج المطرز',\n    description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',\n    category: 'stole',\n    price: 149.99,\n    rental_price: 49.99,\n    colors: ['أبيض مع ذهبي', 'أزرق مع فضي', 'أحمر مع ذهبي'],\n    sizes: ['One Size'],\n    images: ['/images/products/stole-embroidered-1.jpg', '/images/products/stole-embroidered-2.jpg'],\n    stock_quantity: 15,\n    is_available: true,\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-25T10:15:00Z',\n    rating: 4.9,\n    reviews_count: 18,\n    features: ['تطريز يدوي', 'خيوط ذهبية', 'تصميم فاخر'],\n    specifications: {\n      material: 'حرير طبيعي',\n      embroidery: 'خيوط ذهبية وفضية',\n      length: '150 سم'\n    }\n  },\n  {\n    id: '4',\n    name: 'شرابة التخرج الذهبية',\n    description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',\n    category: 'tassel',\n    price: 39.99,\n    rental_price: 15.99,\n    colors: ['ذهبي', 'فضي', 'أزرق', 'أحمر'],\n    sizes: ['One Size'],\n    images: ['/images/products/tassel-gold-1.jpg'],\n    stock_quantity: 100,\n    is_available: true,\n    created_at: '2024-01-18T14:00:00Z',\n    updated_at: '2024-01-26T09:30:00Z',\n    rating: 4.7,\n    reviews_count: 35,\n    features: ['خيوط عالية الجودة', 'ألوان ثابتة', 'سهل التركيب'],\n    specifications: {\n      material: 'خيوط حريرية',\n      length: '23 سم',\n      attachment: 'مشبك معدني'\n    }\n  },\n  {\n    id: '5',\n    name: 'قلنسوة الدكتوراه الفاخرة',\n    description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',\n    category: 'hood',\n    price: 199.99,\n    rental_price: 79.99,\n    colors: ['أسود مع ذهبي', 'أزرق مع فضي'],\n    sizes: ['M', 'L', 'XL'],\n    images: ['/images/products/hood-doctorate-1.jpg', '/images/products/hood-doctorate-2.jpg'],\n    stock_quantity: 8,\n    is_available: true,\n    created_at: '2024-01-19T16:00:00Z',\n    updated_at: '2024-01-27T12:00:00Z',\n    rating: 5.0,\n    reviews_count: 12,\n    features: ['تصميم أكاديمي أصيل', 'خامة فاخرة', 'مناسب للدكتوراه'],\n    specifications: {\n      material: 'مخمل عالي الجودة',\n      lining: 'حرير ملون',\n      academic_level: 'دكتوراه'\n    }\n  }\n]\n\n// بيانات وهمية للمدارس\nexport const mockSchools: MockSchool[] = [\n  {\n    id: '1',\n    admin_id: 'admin-school-1',\n    name: 'جامعة الإمارات العربية المتحدة',\n    name_en: 'United Arab Emirates University',\n    name_fr: 'Université des Émirats Arabes Unis',\n    address: 'شارع الجامعة، العين',\n    city: 'العين',\n    phone: '+971-3-713-5000',\n    email: '<EMAIL>',\n    website: 'https://www.uaeu.ac.ae',\n    logo_url: '/images/schools/uaeu-logo.png',\n    graduation_date: '2024-06-15',\n    student_count: 14500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'قاعة الاحتفالات الكبرى',\n      dress_code: 'formal',\n      photography_allowed: true\n    },\n    created_at: '2024-01-10T08:00:00Z',\n    updated_at: '2024-01-25T10:30:00Z'\n  },\n  {\n    id: '2',\n    admin_id: 'admin-school-2',\n    name: 'الجامعة الأمريكية في الشارقة',\n    name_en: 'American University of Sharjah',\n    name_fr: 'Université Américaine de Sharjah',\n    address: 'شارع الجامعة، الشارقة',\n    city: 'الشارقة',\n    phone: '+971-6-515-5555',\n    email: '<EMAIL>',\n    website: 'https://www.aus.edu',\n    logo_url: '/images/schools/aus-logo.png',\n    graduation_date: '2024-05-20',\n    student_count: 6200,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مسرح الجامعة',\n      dress_code: 'academic',\n      photography_allowed: true\n    },\n    created_at: '2024-01-12T09:15:00Z',\n    updated_at: '2024-01-28T14:20:00Z'\n  },\n  {\n    id: '3',\n    admin_id: 'admin-school-3',\n    name: 'جامعة زايد',\n    name_en: 'Zayed University',\n    name_fr: 'Université Zayed',\n    address: 'شارع الشيخ زايد، دبي',\n    city: 'دبي',\n    phone: '+971-4-402-1111',\n    email: '<EMAIL>',\n    website: 'https://www.zu.ac.ae',\n    logo_url: '/images/schools/zu-logo.png',\n    graduation_date: '2024-06-10',\n    student_count: 9800,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'مركز المؤتمرات',\n      dress_code: 'formal',\n      photography_allowed: false\n    },\n    created_at: '2024-01-15T11:00:00Z',\n    updated_at: '2024-02-01T16:45:00Z'\n  },\n  {\n    id: '4',\n    admin_id: 'admin-school-4',\n    name: 'كلية الإمارات للتكنولوجيا',\n    name_en: 'Emirates Institute of Technology',\n    name_fr: 'Institut de Technologie des Émirats',\n    address: 'المنطقة الأكاديمية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-401-4000',\n    email: '<EMAIL>',\n    website: 'https://www.eit.ac.ae',\n    logo_url: '/images/schools/eit-logo.png',\n    graduation_date: '2024-07-05',\n    student_count: 3500,\n    is_active: true,\n    settings: {\n      graduation_ceremony_location: 'القاعة الرئيسية',\n      dress_code: 'business',\n      photography_allowed: true\n    },\n    created_at: '2024-01-18T13:30:00Z',\n    updated_at: '2024-02-05T09:15:00Z'\n  },\n  {\n    id: '5',\n    admin_id: 'admin-school-5',\n    name: 'معهد أبوظبي للتعليم التقني',\n    name_en: 'Abu Dhabi Technical Institute',\n    name_fr: 'Institut Technique d\\'Abu Dhabi',\n    address: 'المنطقة الصناعية، أبوظبي',\n    city: 'أبوظبي',\n    phone: '+971-2-505-2000',\n    email: '<EMAIL>',\n    website: 'https://www.adti.ac.ae',\n    graduation_date: '2024-06-25',\n    student_count: 2800,\n    is_active: false,\n    settings: {\n      graduation_ceremony_location: 'مركز التدريب',\n      dress_code: 'casual',\n      photography_allowed: true\n    },\n    created_at: '2024-01-20T15:45:00Z',\n    updated_at: '2024-02-10T12:00:00Z'\n  }\n]\n\n// بيانات وهمية للطلبات\nexport const mockOrders: MockOrder[] = [\n  {\n    id: '1',\n    order_number: 'GT-240120-001',\n    customer_id: 'student-1',\n    customer_name: 'أحمد محمد علي',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-50-123-4567',\n    status: 'in_production',\n    items: [\n      {\n        id: '1',\n        order_id: '1',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'أسود',\n          size: 'L',\n          embroidery: 'أحمد علي - بكالوريوس هندسة'\n        }\n      },\n      {\n        id: '2',\n        order_id: '1',\n        product_id: '2',\n        product_name: 'قبعة التخرج الأكاديمية',\n        product_image: '/images/products/cap-academic-1.jpg',\n        category: 'cap',\n        quantity: 1,\n        unit_price: 89.99,\n        total_price: 89.99,\n        customizations: {\n          color: 'أسود',\n          size: 'M'\n        }\n      }\n    ],\n    subtotal: 389.98,\n    tax: 19.50,\n    shipping_cost: 25.00,\n    total: 434.48,\n    payment_status: 'paid',\n    payment_method: 'credit_card',\n    shipping_address: {\n      street: 'شارع الجامعة، مبنى 12، شقة 304',\n      city: 'العين',\n      state: 'أبوظبي',\n      postal_code: '17666',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-001-2024',\n    notes: 'يرجى التسليم قبل حفل التخرج',\n    created_at: '2024-01-20T10:30:00Z',\n    updated_at: '2024-01-22T14:15:00Z',\n    delivery_date: '2024-02-15T00:00:00Z',\n    school_id: '1',\n    school_name: 'جامعة الإمارات العربية المتحدة'\n  },\n  {\n    id: '2',\n    order_number: 'GT-**********',\n    customer_id: 'student-2',\n    customer_name: 'فاطمة سالم الزهراني',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-56-789-0123',\n    status: 'delivered',\n    items: [\n      {\n        id: '3',\n        order_id: '2',\n        product_id: '3',\n        product_name: 'ثوب التخرج المميز',\n        product_image: '/images/products/gown-premium-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 399.99,\n        total_price: 399.99,\n        customizations: {\n          color: 'أزرق داكن',\n          size: 'M',\n          embroidery: 'فاطمة الزهراني - ماجستير إدارة أعمال'\n        }\n      }\n    ],\n    subtotal: 399.99,\n    tax: 20.00,\n    shipping_cost: 30.00,\n    total: 449.99,\n    payment_status: 'paid',\n    payment_method: 'bank_transfer',\n    shipping_address: {\n      street: 'شارع الكورنيش، برج الإمارات، الطابق 15',\n      city: 'الشارقة',\n      state: 'الشارقة',\n      postal_code: '27272',\n      country: 'الإمارات العربية المتحدة'\n    },\n    tracking_number: 'TRK-GT-002-2024',\n    created_at: '2024-01-21T09:15:00Z',\n    updated_at: '2024-01-25T16:30:00Z',\n    delivery_date: '2024-01-28T00:00:00Z',\n    school_id: '2',\n    school_name: 'الجامعة الأمريكية في الشارقة'\n  },\n  {\n    id: '3',\n    order_number: 'GT-**********',\n    customer_id: 'student-3',\n    customer_name: 'خالد عبدالله المنصوري',\n    customer_email: '<EMAIL>',\n    customer_phone: '+971-52-456-7890',\n    status: 'pending',\n    items: [\n      {\n        id: '4',\n        order_id: '3',\n        product_id: '1',\n        product_name: 'ثوب التخرج الكلاسيكي',\n        product_image: '/images/products/gown-classic-1.jpg',\n        category: 'gown',\n        quantity: 1,\n        unit_price: 299.99,\n        total_price: 299.99,\n        customizations: {\n          color: 'بورجوندي',\n          size: 'XL'\n        }\n      },\n      {\n        id: '5',\n        order_id: '3',\n        product_id: '4',\n        product_name: 'وشاح التخرج المطرز',\n        product_image: '/images/products/stole-embroidered-1.jpg',\n        category: 'stole',\n        quantity: 1,\n        unit_price: 149.99,\n        total_price: 149.99,\n        customizations: {\n          color: 'ذهبي',\n          embroidery: 'كلية الهندسة'\n        }\n      }\n    ],\n    subtotal: 449.98,\n    tax: 22.50,\n    shipping_cost: 25.00,\n    total: 497.48,\n    payment_status: 'pending',\n    shipping_address: {\n      street: 'شارع الشيخ زايد، مجمع دبي الأكاديمي',\n      city: 'دبي',\n      state: 'دبي',\n      postal_code: '391186',\n      country: 'الإمارات العربية المتحدة'\n    },\n    created_at: '2024-01-22T14:45:00Z',\n    updated_at: '2024-01-22T14:45:00Z',\n    school_id: '3',\n    school_name: 'جامعة زايد'\n  }\n]\n\n// مساعدات للتعامل مع البيانات الوهمية\nexport class MockDataManager {\n  private static getStorageKey(type: 'pages' | 'menuItems' | 'products' | 'categories' | 'schools' | 'orders'): string {\n    return `mockData_${type}`\n  }\n\n  static getPages(): MockPage[] {\n    if (typeof window === 'undefined') return mockPages\n\n    const stored = localStorage.getItem(this.getStorageKey('pages'))\n    return stored ? JSON.parse(stored) : mockPages\n  }\n\n  static getMenuItems(): MockMenuItem[] {\n    if (typeof window === 'undefined') return mockMenuItems\n\n    const stored = localStorage.getItem(this.getStorageKey('menuItems'))\n    return stored ? JSON.parse(stored) : mockMenuItems\n  }\n\n  static getProducts(): MockProduct[] {\n    if (typeof window === 'undefined') return mockProducts\n\n    const stored = localStorage.getItem(this.getStorageKey('products'))\n    return stored ? JSON.parse(stored) : mockProducts\n  }\n\n  static getCategories(): MockCategory[] {\n    if (typeof window === 'undefined') return mockCategories\n\n    const stored = localStorage.getItem(this.getStorageKey('categories'))\n    return stored ? JSON.parse(stored) : mockCategories\n  }\n\n  static getSchools(): MockSchool[] {\n    if (typeof window === 'undefined') return mockSchools\n\n    const stored = localStorage.getItem(this.getStorageKey('schools'))\n    return stored ? JSON.parse(stored) : mockSchools\n  }\n\n  static getOrders(): MockOrder[] {\n    if (typeof window === 'undefined') return mockOrders\n\n    const stored = localStorage.getItem(this.getStorageKey('orders'))\n    return stored ? JSON.parse(stored) : mockOrders\n  }\n\n  static savePages(pages: MockPage[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('pages'), JSON.stringify(pages))\n    }\n  }\n\n  static saveMenuItems(items: MockMenuItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('menuItems'), JSON.stringify(items))\n    }\n  }\n\n  static saveProducts(products: MockProduct[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('products'), JSON.stringify(products))\n    }\n  }\n\n  static saveCategories(categories: MockCategory[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('categories'), JSON.stringify(categories))\n    }\n  }\n\n  static saveSchools(schools: MockSchool[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('schools'), JSON.stringify(schools))\n    }\n  }\n\n  static saveOrders(orders: MockOrder[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('orders'), JSON.stringify(orders))\n    }\n  }\n\n  static generateId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 11)\n  }\n\n  static generateOrderNumber(): string {\n    const date = new Date()\n    const year = date.getFullYear().toString().slice(-2)\n    const month = (date.getMonth() + 1).toString().padStart(2, '0')\n    const day = date.getDate().toString().padStart(2, '0')\n    const orders = this.getOrders()\n    const todayOrders = orders.filter(order =>\n      order.created_at.startsWith(`${date.getFullYear()}-${month}-${day}`)\n    )\n    const orderCount = (todayOrders.length + 1).toString().padStart(3, '0')\n    return `GT-${year}${month}${day}-${orderCount}`\n  }\n\n  // إدارة نماذج الذكاء الاصطناعي\n  static getAIModels(): AIModel[] {\n    const stored = localStorage.getItem('mockAIModels')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultAIModels\n  }\n\n  static saveAIModels(models: AIModel[]): void {\n    localStorage.setItem('mockAIModels', JSON.stringify(models))\n  }\n\n  static getModelActivities(): ModelActivity[] {\n    const stored = localStorage.getItem('mockModelActivities')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultModelActivities\n  }\n\n  static saveModelActivities(activities: ModelActivity[]): void {\n    localStorage.setItem('mockModelActivities', JSON.stringify(activities))\n  }\n\n  // إدارة قوالب الصفحات\n  static getPageTemplates(): PageTemplate[] {\n    const stored = localStorage.getItem('mockPageTemplates')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultPageTemplates\n  }\n\n  static savePageTemplates(templates: PageTemplate[]): void {\n    localStorage.setItem('mockPageTemplates', JSON.stringify(templates))\n  }\n\n  static getPageProjects(): PageProject[] {\n    const stored = localStorage.getItem('mockPageProjects')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultPageProjects\n  }\n\n  static savePageProjects(projects: PageProject[]): void {\n    localStorage.setItem('mockPageProjects', JSON.stringify(projects))\n  }\n\n  static getComponentLibrary(): ComponentLibraryItem[] {\n    const stored = localStorage.getItem('mockComponentLibrary')\n    if (stored) {\n      return JSON.parse(stored)\n    }\n    return this.defaultComponentLibrary\n  }\n\n  static saveComponentLibrary(components: ComponentLibraryItem[]): void {\n    localStorage.setItem('mockComponentLibrary', JSON.stringify(components))\n  }\n\n  // البيانات الافتراضية لنماذج الذكاء الاصطناعي\n  static defaultAIModels: AIModel[] = [\n    {\n      id: 'openai-gpt',\n      name: 'OpenAI GPT',\n      provider: 'openai',\n      type: 'text',\n      description: 'نماذج OpenAI للنصوص والمحادثة',\n      baseUrl: 'https://api.openai.com/v1',\n      selectedModels: ['gpt-4-turbo', 'gpt-3.5-turbo'],\n      subModels: [\n        {\n          id: 'gpt-4-turbo',\n          name: 'gpt-4-turbo',\n          modelId: 'openai-gpt',\n          description: 'أحدث نموذج GPT-4 مع أداء محسن',\n          version: '2024-04-09',\n          capabilities: ['text-generation', 'conversation', 'code', 'analysis'],\n          pricing: { inputTokens: 0.01, outputTokens: 0.03, currency: 'USD', unit: '1K tokens' },\n          limits: { maxTokens: 128000, requestsPerMinute: 500, requestsPerDay: 10000, contextWindow: 128000 },\n          isActive: true,\n          isDefault: true,\n          tags: ['latest', 'recommended'],\n          releaseDate: '2024-04-09T00:00:00Z'\n        },\n        {\n          id: 'gpt-3.5-turbo',\n          name: 'gpt-3.5-turbo',\n          modelId: 'openai-gpt',\n          description: 'نموذج سريع وفعال للمحادثة',\n          version: '0125',\n          capabilities: ['text-generation', 'conversation'],\n          pricing: { inputTokens: 0.0005, outputTokens: 0.0015, currency: 'USD', unit: '1K tokens' },\n          limits: { maxTokens: 16385, requestsPerMinute: 3500, requestsPerDay: 50000, contextWindow: 16385 },\n          isActive: true,\n          isDefault: false,\n          tags: ['fast', 'economical'],\n          releaseDate: '2024-01-25T00:00:00Z'\n        }\n      ],\n      isActive: true,\n      status: 'active',\n      settings: {\n        temperature: 0.7,\n        maxTokens: 2048,\n        topP: 1,\n        frequencyPenalty: 0,\n        presencePenalty: 0\n      },\n      usage: {\n        totalRequests: 1247,\n        totalTokens: 156789,\n        totalCost: 23.45,\n        lastUsed: '2024-01-20T10:30:00Z',\n        dailyUsage: [],\n        monthlyUsage: [],\n        averageResponseTime: 1200,\n        successRate: 98.5\n      },\n      createdAt: '2024-01-01T00:00:00Z',\n      updatedAt: '2024-01-20T10:30:00Z',\n      icon: '🤖',\n      color: '#10B981',\n      website: 'https://openai.com',\n      documentation: 'https://platform.openai.com/docs'\n    },\n    {\n      id: 'anthropic-claude',\n      name: 'Anthropic Claude',\n      provider: 'anthropic',\n      type: 'text',\n      description: 'نماذج Claude للمحادثة والتحليل',\n      baseUrl: 'https://api.anthropic.com',\n      selectedModels: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229'],\n      subModels: [\n        {\n          id: 'claude-3-opus',\n          name: 'claude-3-opus-20240229',\n          modelId: 'anthropic-claude',\n          description: 'أقوى نموذج من Claude للمهام المعقدة',\n          version: '20240229',\n          capabilities: ['text-generation', 'analysis', 'reasoning', 'code'],\n          pricing: { inputTokens: 0.015, outputTokens: 0.075, currency: 'USD', unit: '1K tokens' },\n          limits: { maxTokens: 200000, requestsPerMinute: 50, requestsPerDay: 1000, contextWindow: 200000 },\n          isActive: true,\n          isDefault: true,\n          tags: ['powerful', 'reasoning'],\n          releaseDate: '2024-02-29T00:00:00Z'\n        },\n        {\n          id: 'claude-3-sonnet',\n          name: 'claude-3-sonnet-20240229',\n          modelId: 'anthropic-claude',\n          description: 'نموذج متوازن بين الأداء والسرعة',\n          version: '20240229',\n          capabilities: ['text-generation', 'analysis', 'reasoning'],\n          pricing: { inputTokens: 0.003, outputTokens: 0.015, currency: 'USD', unit: '1K tokens' },\n          limits: { maxTokens: 200000, requestsPerMinute: 100, requestsPerDay: 2000, contextWindow: 200000 },\n          isActive: true,\n          isDefault: false,\n          tags: ['balanced', 'efficient'],\n          releaseDate: '2024-02-29T00:00:00Z'\n        }\n      ],\n      isActive: true,\n      status: 'active',\n      settings: {\n        temperature: 0.7,\n        maxTokens: 4096\n      },\n      usage: {\n        totalRequests: 456,\n        totalTokens: 89123,\n        totalCost: 45.67,\n        lastUsed: '2024-01-19T15:45:00Z',\n        dailyUsage: [],\n        monthlyUsage: [],\n        averageResponseTime: 2100,\n        successRate: 99.1\n      },\n      createdAt: '2024-01-01T00:00:00Z',\n      updatedAt: '2024-01-19T15:45:00Z',\n      icon: '🧠',\n      color: '#8B5CF6',\n      website: 'https://anthropic.com',\n      documentation: 'https://docs.anthropic.com'\n    }\n  ]\n\n  static defaultModelActivities: ModelActivity[] = [\n    {\n      id: 'activity-1',\n      modelId: 'openai-gpt',\n      subModelId: 'gpt-4-turbo',\n      type: 'request',\n      description: 'توليد وصف منتج لأزياء التخرج',\n      userId: 'admin-1',\n      timestamp: '2024-01-20T10:30:00Z',\n      duration: 1200,\n      tokensUsed: 156,\n      cost: 0.05,\n      success: true\n    },\n    {\n      id: 'activity-2',\n      modelId: 'anthropic-claude',\n      subModelId: 'claude-3-opus',\n      type: 'request',\n      description: 'تحليل تفضيلات العملاء',\n      userId: 'admin-1',\n      timestamp: '2024-01-20T09:15:00Z',\n      duration: 2100,\n      tokensUsed: 234,\n      cost: 0.12,\n      success: true\n    }\n  ]\n\n  // البيانات الافتراضية لقوالب الصفحات\n  static defaultPageTemplates: PageTemplate[] = [\n    {\n      id: 'template-landing-1',\n      name: 'Landing Page - Modern',\n      nameAr: 'صفحة هبوط - عصرية',\n      nameEn: 'Landing Page - Modern',\n      nameFr: 'Page d\\'atterrissage - Moderne',\n      description: 'قالب صفحة هبوط عصرية مع تصميم جذاب',\n      category: 'landing',\n      components: [\n        {\n          id: 'hero-1',\n          type: 'hero',\n          name: 'Hero Section',\n          props: {\n            content: 'مرحباً بكم في منصة أزياء التخرج',\n            style: { backgroundColor: '#1F2937', color: '#FFFFFF' }\n          },\n          position: { x: 0, y: 0 },\n          size: { width: '100%', height: '500px' },\n          isVisible: true\n        }\n      ],\n      preview: '/images/templates/landing-modern.jpg',\n      thumbnail: '/images/templates/landing-modern-thumb.jpg',\n      isAIGenerated: false,\n      isPremium: false,\n      tags: ['landing', 'modern', 'business'],\n      createdAt: '2024-01-01T00:00:00Z',\n      updatedAt: '2024-01-01T00:00:00Z',\n      usageCount: 45,\n      rating: 4.8,\n      metadata: {\n        colors: ['#1F2937', '#FFFFFF', '#3B82F6'],\n        fonts: ['Inter', 'Cairo'],\n        layout: 'single-page',\n        responsive: true\n      }\n    }\n  ]\n\n  // البيانات الافتراضية لمشاريع الصفحات\n  static defaultPageProjects: PageProject[] = [\n    {\n      id: 'project-1',\n      name: 'موقع أزياء التخرج الرئيسي',\n      description: 'الموقع الرئيسي لعرض منتجات أزياء التخرج',\n      components: [],\n      templateId: 'template-landing-1',\n      generationMode: 'template',\n      settings: {\n        title: 'أزياء التخرج - منصة مغربية متخصصة',\n        description: 'أول منصة مغربية لتأجير وبيع أزياء التخرج',\n        keywords: ['أزياء التخرج', 'تأجير', 'المغرب'],\n        language: 'ar',\n        direction: 'rtl'\n      },\n      isPublished: false,\n      createdAt: '2024-01-15T00:00:00Z',\n      updatedAt: '2024-01-20T10:30:00Z',\n      createdBy: 'admin-1',\n      version: 1\n    }\n  ]\n\n  // البيانات الافتراضية لمكتبة المكونات\n  static defaultComponentLibrary: ComponentLibraryItem[] = [\n    {\n      id: 'comp-hero',\n      name: 'Hero Section',\n      nameAr: 'قسم البطل',\n      type: 'hero',\n      category: 'layout',\n      description: 'قسم رئيسي جذاب في أعلى الصفحة',\n      icon: 'Layout',\n      preview: '/images/components/hero-preview.jpg',\n      defaultProps: {\n        content: 'عنوان رئيسي جذاب',\n        style: { backgroundColor: '#1F2937', color: '#FFFFFF' }\n      },\n      defaultSize: { width: '100%', height: '500px' },\n      isCustom: false,\n      isPremium: false,\n      tags: ['layout', 'header', 'hero'],\n      usageCount: 156\n    },\n    {\n      id: 'comp-button',\n      name: 'Button',\n      nameAr: 'زر',\n      type: 'button',\n      category: 'interactive',\n      description: 'زر تفاعلي قابل للتخصيص',\n      icon: 'MousePointer',\n      preview: '/images/components/button-preview.jpg',\n      defaultProps: {\n        content: 'انقر هنا',\n        style: { backgroundColor: '#3B82F6', color: '#FFFFFF' }\n      },\n      defaultSize: { width: '120px', height: '40px' },\n      isCustom: false,\n      isPremium: false,\n      tags: ['interactive', 'button', 'cta'],\n      usageCount: 234\n    }\n  ]\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;AAoJ1B,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;CACD;AAGM,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,iBAAiC;IAC5C;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAa;SAAW;QACzC,OAAO;YAAC;YAAK;YAAK;YAAK;YAAM;SAAM;QACnC,QAAQ;YAAC;YAAuC;SAAsC;QACtF,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAkB;YAAe;SAAoB;QAChE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;SAAY;QAC7B,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAyC;QAClD,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAA0B;YAAe;SAAe;QACnE,gBAAgB;YACd,UAAU;YACV,cAAc;YACd,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;YAAe;SAAe;QACvD,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;YAA4C;SAA2C;QAChG,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAc;YAAc;SAAa;QACpD,gBAAgB;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAO;YAAQ;SAAO;QACvC,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAqC;QAC9C,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAqB;YAAe;SAAc;QAC7D,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;SAAc;QACvC,OAAO;YAAC;YAAK;YAAK;SAAK;QACvB,QAAQ;YAAC;YAAyC;SAAwC;QAC1F,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAsB;YAAc;SAAkB;QACjE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,gBAAgB;QAClB;IACF;CACD;AAGM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,WAAW;QACX,UAAU;YACR,8BAA8B;YAC9B,YAAY;YACZ,qBAAqB;QACvB;QACA,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,MAAM;gBACR;YACF;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,gBAAgB;oBACd,OAAO;oBACP,YAAY;gBACd;YACF;SACD;QACD,UAAU;QACV,KAAK;QACL,eAAe;QACf,OAAO;QACP,gBAAgB;QAChB,kBAAkB;YAChB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,aAAa;IACf;CACD;AAGM,MAAM;IACX,OAAe,cAAc,IAA8E,EAAU;QACnH,OAAO,CAAC,SAAS,EAAE,MAAM;IAC3B;IAEA,OAAO,WAAuB;QAC5B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,eAA+B;QACpC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,cAA6B;QAClC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,gBAAgC;QACrC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,aAA2B;QAChC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,YAAyB;QAC9B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,UAAU,KAAiB,EAAQ;QACxC,uCAAmC;;QAEnC;IACF;IAEA,OAAO,cAAc,KAAqB,EAAQ;QAChD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAa,QAAuB,EAAQ;QACjD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,eAAe,UAA0B,EAAQ;QACtD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,YAAY,OAAqB,EAAQ;QAC9C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,WAAW,MAAmB,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAqB;QAC1B,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACzE;IAEA,OAAO,sBAA8B;QACnC,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,WAAW,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;QAClD,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAC3D,MAAM,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAClD,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA,QAChC,MAAM,UAAU,CAAC,UAAU,CAAC,GAAG,KAAK,WAAW,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;QAErE,MAAM,aAAa,CAAC,YAAY,MAAM,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;QACnE,OAAO,CAAC,GAAG,EAAE,OAAO,QAAQ,IAAI,CAAC,EAAE,YAAY;IACjD;IAEA,+BAA+B;IAC/B,OAAO,cAAyB;QAC9B,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA,OAAO,aAAa,MAAiB,EAAQ;QAC3C,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;IACtD;IAEA,OAAO,qBAAsC;QAC3C,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO,IAAI,CAAC,sBAAsB;IACpC;IAEA,OAAO,oBAAoB,UAA2B,EAAQ;QAC5D,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;IAC7D;IAEA,sBAAsB;IACtB,OAAO,mBAAmC;QACxC,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO,IAAI,CAAC,oBAAoB;IAClC;IAEA,OAAO,kBAAkB,SAAyB,EAAQ;QACxD,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,OAAO,kBAAiC;QACtC,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO,IAAI,CAAC,mBAAmB;IACjC;IAEA,OAAO,iBAAiB,QAAuB,EAAQ;QACrD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;IAC1D;IAEA,OAAO,sBAA8C;QACnD,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO,IAAI,CAAC,uBAAuB;IACrC;IAEA,OAAO,qBAAqB,UAAkC,EAAQ;QACpE,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;IAC9D;IAEA,8CAA8C;IAC9C,OAAO,kBAA6B;QAClC;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,MAAM;YACN,aAAa;YACb,SAAS;YACT,gBAAgB;gBAAC;gBAAe;aAAgB;YAChD,WAAW;gBACT;oBACE,IAAI;oBACJ,MAAM;oBACN,SAAS;oBACT,aAAa;oBACb,SAAS;oBACT,cAAc;wBAAC;wBAAmB;wBAAgB;wBAAQ;qBAAW;oBACrE,SAAS;wBAAE,aAAa;wBAAM,cAAc;wBAAM,UAAU;wBAAO,MAAM;oBAAY;oBACrF,QAAQ;wBAAE,WAAW;wBAAQ,mBAAmB;wBAAK,gBAAgB;wBAAO,eAAe;oBAAO;oBAClG,UAAU;oBACV,WAAW;oBACX,MAAM;wBAAC;wBAAU;qBAAc;oBAC/B,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,SAAS;oBACT,aAAa;oBACb,SAAS;oBACT,cAAc;wBAAC;wBAAmB;qBAAe;oBACjD,SAAS;wBAAE,aAAa;wBAAQ,cAAc;wBAAQ,UAAU;wBAAO,MAAM;oBAAY;oBACzF,QAAQ;wBAAE,WAAW;wBAAO,mBAAmB;wBAAM,gBAAgB;wBAAO,eAAe;oBAAM;oBACjG,UAAU;oBACV,WAAW;oBACX,MAAM;wBAAC;wBAAQ;qBAAa;oBAC5B,aAAa;gBACf;aACD;YACD,UAAU;YACV,QAAQ;YACR,UAAU;gBACR,aAAa;gBACb,WAAW;gBACX,MAAM;gBACN,kBAAkB;gBAClB,iBAAiB;YACnB;YACA,OAAO;gBACL,eAAe;gBACf,aAAa;gBACb,WAAW;gBACX,UAAU;gBACV,YAAY,EAAE;gBACd,cAAc,EAAE;gBAChB,qBAAqB;gBACrB,aAAa;YACf;YACA,WAAW;YACX,WAAW;YACX,MAAM;YACN,OAAO;YACP,SAAS;YACT,eAAe;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,MAAM;YACN,aAAa;YACb,SAAS;YACT,gBAAgB;gBAAC;gBAA0B;aAA2B;YACtE,WAAW;gBACT;oBACE,IAAI;oBACJ,MAAM;oBACN,SAAS;oBACT,aAAa;oBACb,SAAS;oBACT,cAAc;wBAAC;wBAAmB;wBAAY;wBAAa;qBAAO;oBAClE,SAAS;wBAAE,aAAa;wBAAO,cAAc;wBAAO,UAAU;wBAAO,MAAM;oBAAY;oBACvF,QAAQ;wBAAE,WAAW;wBAAQ,mBAAmB;wBAAI,gBAAgB;wBAAM,eAAe;oBAAO;oBAChG,UAAU;oBACV,WAAW;oBACX,MAAM;wBAAC;wBAAY;qBAAY;oBAC/B,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,SAAS;oBACT,aAAa;oBACb,SAAS;oBACT,cAAc;wBAAC;wBAAmB;wBAAY;qBAAY;oBAC1D,SAAS;wBAAE,aAAa;wBAAO,cAAc;wBAAO,UAAU;wBAAO,MAAM;oBAAY;oBACvF,QAAQ;wBAAE,WAAW;wBAAQ,mBAAmB;wBAAK,gBAAgB;wBAAM,eAAe;oBAAO;oBACjG,UAAU;oBACV,WAAW;oBACX,MAAM;wBAAC;wBAAY;qBAAY;oBAC/B,aAAa;gBACf;aACD;YACD,UAAU;YACV,QAAQ;YACR,UAAU;gBACR,aAAa;gBACb,WAAW;YACb;YACA,OAAO;gBACL,eAAe;gBACf,aAAa;gBACb,WAAW;gBACX,UAAU;gBACV,YAAY,EAAE;gBACd,cAAc,EAAE;gBAChB,qBAAqB;gBACrB,aAAa;YACf;YACA,WAAW;YACX,WAAW;YACX,MAAM;YACN,OAAO;YACP,SAAS;YACT,eAAe;QACjB;KACD,CAAA;IAED,OAAO,yBAA0C;QAC/C;YACE,IAAI;YACJ,SAAS;YACT,YAAY;YACZ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,WAAW;YACX,UAAU;YACV,YAAY;YACZ,MAAM;YACN,SAAS;QACX;QACA;YACE,IAAI;YACJ,SAAS;YACT,YAAY;YACZ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,WAAW;YACX,UAAU;YACV,YAAY;YACZ,MAAM;YACN,SAAS;QACX;KACD,CAAA;IAED,qCAAqC;IACrC,OAAO,uBAAuC;QAC5C;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,UAAU;YACV,YAAY;gBACV;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,OAAO;wBACL,SAAS;wBACT,OAAO;4BAAE,iBAAiB;4BAAW,OAAO;wBAAU;oBACxD;oBACA,UAAU;wBAAE,GAAG;wBAAG,GAAG;oBAAE;oBACvB,MAAM;wBAAE,OAAO;wBAAQ,QAAQ;oBAAQ;oBACvC,WAAW;gBACb;aACD;YACD,SAAS;YACT,WAAW;YACX,eAAe;YACf,WAAW;YACX,MAAM;gBAAC;gBAAW;gBAAU;aAAW;YACvC,WAAW;YACX,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,UAAU;gBACR,QAAQ;oBAAC;oBAAW;oBAAW;iBAAU;gBACzC,OAAO;oBAAC;oBAAS;iBAAQ;gBACzB,QAAQ;gBACR,YAAY;YACd;QACF;KACD,CAAA;IAED,sCAAsC;IACtC,OAAO,sBAAqC;QAC1C;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,YAAY,EAAE;YACd,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBACR,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAC;oBAAgB;oBAAS;iBAAS;gBAC7C,UAAU;gBACV,WAAW;YACb;YACA,aAAa;YACb,WAAW;YACX,WAAW;YACX,WAAW;YACX,SAAS;QACX;KACD,CAAA;IAED,sCAAsC;IACtC,OAAO,0BAAkD;QACvD;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,aAAa;YACb,MAAM;YACN,SAAS;YACT,cAAc;gBACZ,SAAS;gBACT,OAAO;oBAAE,iBAAiB;oBAAW,OAAO;gBAAU;YACxD;YACA,aAAa;gBAAE,OAAO;gBAAQ,QAAQ;YAAQ;YAC9C,UAAU;YACV,WAAW;YACX,MAAM;gBAAC;gBAAU;gBAAU;aAAO;YAClC,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,aAAa;YACb,MAAM;YACN,SAAS;YACT,cAAc;gBACZ,SAAS;gBACT,OAAO;oBAAE,iBAAiB;oBAAW,OAAO;gBAAU;YACxD;YACA,aAAa;gBAAE,OAAO;gBAAS,QAAQ;YAAO;YAC9C,UAAU;YACV,WAAW;YACX,MAAM;gBAAC;gBAAe;gBAAU;aAAM;YACtC,YAAY;QACd;KACD,CAAA;AACH", "debugId": null}}, {"offset": {"line": 1315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/ai-models/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { MockDataManager } from '@/lib/mockData'\nimport { AIModel, CreateModelRequest } from '@/types/ai-models'\n\n// GET - جلب جميع نماذج الذكاء الاصطناعي\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const provider = searchParams.get('provider')\n    const type = searchParams.get('type')\n    const status = searchParams.get('status')\n    const includeInactive = searchParams.get('include_inactive') === 'true'\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n\n    // جلب البيانات الوهمية\n    let models = MockDataManager.getAIModels()\n\n    // تطبيق الفلاتر\n    if (provider) {\n      models = models.filter(model => model.provider === provider)\n    }\n\n    if (type) {\n      models = models.filter(model => model.type === type)\n    }\n\n    if (status) {\n      models = models.filter(model => model.status === status)\n    }\n\n    if (!includeInactive) {\n      models = models.filter(model => model.isActive)\n    }\n\n    // تطبيق التصفح\n    const startIndex = (page - 1) * limit\n    const endIndex = startIndex + limit\n    const paginatedModels = models.slice(startIndex, endIndex)\n\n    // إحصائيات إضافية\n    const stats = {\n      total: models.length,\n      active: models.filter(m => m.isActive).length,\n      inactive: models.filter(m => !m.isActive).length,\n      byProvider: models.reduce((acc, model) => {\n        acc[model.provider] = (acc[model.provider] || 0) + 1\n        return acc\n      }, {} as Record<string, number>),\n      byType: models.reduce((acc, model) => {\n        acc[model.type] = (acc[model.type] || 0) + 1\n        return acc\n      }, {} as Record<string, number>)\n    }\n\n    return NextResponse.json({\n      models: paginatedModels,\n      total: models.length,\n      page,\n      limit,\n      totalPages: Math.ceil(models.length / limit),\n      stats\n    })\n\n  } catch (error) {\n    console.error('Error fetching AI models:', error)\n    return NextResponse.json(\n      { error: 'خطأ في جلب نماذج الذكاء الاصطناعي' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة نموذج ذكاء اصطناعي جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const body: CreateModelRequest = await request.json()\n    const {\n      name,\n      provider,\n      type,\n      description,\n      apiKey,\n      apiEndpoint,\n      baseUrl,\n      settings,\n      selectedModels,\n      subModels\n    } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!provider) {\n      return NextResponse.json(\n        { error: 'مقدم الخدمة مطلوب' },\n        { status: 400 }\n      )\n    }\n\n    // التحقق من وجود baseUrl\n    if (!baseUrl) {\n      return NextResponse.json(\n        { error: 'Base URL مطلوب' },\n        { status: 400 }\n      )\n    }\n\n    // التحقق من تحديد نماذج\n    if (!selectedModels || selectedModels.length === 0) {\n      return NextResponse.json(\n        { error: 'يجب تحديد نموذج واحد على الأقل' },\n        { status: 400 }\n      )\n    }\n\n    // جلب النماذج الحالية\n    const models = MockDataManager.getAIModels()\n\n    // التحقق من عدم تكرار مقدم الخدمة\n    const existingModel = models.find(model => model.provider === provider)\n    if (existingModel) {\n      return NextResponse.json(\n        { error: 'مقدم الخدمة موجود بالفعل. يمكنك تحرير الإعدادات الموجودة.' },\n        { status: 400 }\n      )\n    }\n\n    // توليد اسم النموذج من مقدم الخدمة\n    const providerNames = {\n      'openai': 'OpenAI',\n      'anthropic': 'Anthropic Claude',\n      'google': 'Google Gemini',\n      'meta': 'Meta LLaMA',\n      'stability': 'Stability AI',\n      'cohere': 'Cohere',\n      'huggingface': 'Hugging Face',\n      'deepseek': 'DeepSeek'\n    }\n\n    const generatedName = name || providerNames[provider] || provider\n\n    // تحديد نوع النموذج من النماذج المحددة\n    const modelType = type || determineModelType(selectedModels || [])\n\n    // إنشاء النموذج الجديد\n    const newModel: AIModel = {\n      id: MockDataManager.generateId(),\n      name: generatedName,\n      provider,\n      type: modelType,\n      description: description || `نماذج ${generatedName} للذكاء الاصطناعي`,\n      subModels: subModels || [],\n      selectedModels: selectedModels || [],\n      apiKey,\n      baseUrl,\n      isActive: true,\n      status: 'inactive', // يبدأ غير نشط حتى يتم اختباره\n      settings: settings || {\n        temperature: 0.7,\n        maxTokens: 2048,\n        topP: 1,\n        frequencyPenalty: 0,\n        presencePenalty: 0\n      },\n      usage: {\n        totalRequests: 0,\n        totalTokens: 0,\n        totalCost: 0,\n        dailyUsage: [],\n        monthlyUsage: [],\n        averageResponseTime: 0,\n        successRate: 0\n      },\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n\n    // دالة مساعدة لتحديد نوع النموذج\n    function determineModelType(models: string[]): any {\n      const hasImageModel = models.some(m =>\n        m.includes('dall-e') || m.includes('stable-diffusion') || m.includes('vision')\n      )\n      const hasAudioModel = models.some(m =>\n        m.includes('whisper') || m.includes('tts')\n      )\n      const hasEmbeddingModel = models.some(m =>\n        m.includes('embed')\n      )\n      const hasCodeModel = models.some(m =>\n        m.includes('code') || m.includes('coder')\n      )\n      const hasMultimodalModel = models.some(m =>\n        m.includes('vision') || m.includes('multimodal')\n      )\n\n      if (hasMultimodalModel) return 'multimodal'\n      if (hasImageModel) return 'image'\n      if (hasAudioModel) return 'audio'\n      if (hasEmbeddingModel) return 'embedding'\n      if (hasCodeModel) return 'code'\n\n      return 'text' // افتراضي\n    }\n\n    // حفظ النموذج\n    models.push(newModel)\n    MockDataManager.saveAIModels(models)\n\n    // إضافة نشاط\n    const activities = MockDataManager.getModelActivities()\n    activities.push({\n      id: MockDataManager.generateId(),\n      modelId: newModel.id,\n      type: 'config_change',\n      description: `تم إضافة مقدم خدمة جديد: ${generatedName}`,\n      timestamp: new Date().toISOString(),\n      success: true\n    })\n    MockDataManager.saveModelActivities(activities)\n\n    return NextResponse.json({ \n      message: 'تم إضافة النموذج بنجاح',\n      model: newModel \n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Error creating AI model:', error)\n    return NextResponse.json(\n      { error: 'خطأ في إنشاء النموذج' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT - تحديث إعدادات عامة للنماذج\nexport async function PUT(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { action, modelIds, settings } = body\n\n    if (!action || !modelIds || !Array.isArray(modelIds)) {\n      return NextResponse.json(\n        { error: 'الإجراء ومعرفات النماذج مطلوبة' },\n        { status: 400 }\n      )\n    }\n\n    const models = MockDataManager.getAIModels()\n    const activities = MockDataManager.getModelActivities()\n    let updatedCount = 0\n\n    for (const modelId of modelIds) {\n      const modelIndex = models.findIndex(m => m.id === modelId)\n      if (modelIndex === -1) continue\n\n      const model = models[modelIndex]\n      let description = ''\n\n      switch (action) {\n        case 'activate':\n          model.isActive = true\n          model.status = 'active'\n          description = `تم تفعيل النموذج: ${model.name}`\n          break\n        case 'deactivate':\n          model.isActive = false\n          model.status = 'inactive'\n          description = `تم إلغاء تفعيل النموذج: ${model.name}`\n          break\n        case 'update_settings':\n          if (settings) {\n            model.settings = { ...model.settings, ...settings }\n            description = `تم تحديث إعدادات النموذج: ${model.name}`\n          }\n          break\n        default:\n          continue\n      }\n\n      model.updatedAt = new Date().toISOString()\n      models[modelIndex] = model\n      updatedCount++\n\n      // إضافة نشاط\n      activities.push({\n        id: MockDataManager.generateId(),\n        modelId: model.id,\n        type: 'config_change',\n        description,\n        timestamp: new Date().toISOString(),\n        success: true\n      })\n    }\n\n    // حفظ التغييرات\n    MockDataManager.saveAIModels(models)\n    MockDataManager.saveModelActivities(activities)\n\n    return NextResponse.json({\n      message: `تم تحديث ${updatedCount} نموذج بنجاح`,\n      updatedCount\n    })\n\n  } catch (error) {\n    console.error('Error updating AI models:', error)\n    return NextResponse.json(\n      { error: 'خطأ في تحديث النماذج' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE - حذف نماذج متعددة\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const modelIds = searchParams.get('ids')?.split(',') || []\n\n    if (modelIds.length === 0) {\n      return NextResponse.json(\n        { error: 'معرفات النماذج مطلوبة' },\n        { status: 400 }\n      )\n    }\n\n    const models = MockDataManager.getAIModels()\n    const activities = MockDataManager.getModelActivities()\n    let deletedCount = 0\n\n    // حذف النماذج\n    const remainingModels = models.filter(model => {\n      if (modelIds.includes(model.id)) {\n        // إضافة نشاط الحذف\n        activities.push({\n          id: MockDataManager.generateId(),\n          modelId: model.id,\n          type: 'config_change',\n          description: `تم حذف النموذج: ${model.name}`,\n          timestamp: new Date().toISOString(),\n          success: true\n        })\n        deletedCount++\n        return false\n      }\n      return true\n    })\n\n    // حفظ التغييرات\n    MockDataManager.saveAIModels(remainingModels)\n    MockDataManager.saveModelActivities(activities)\n\n    return NextResponse.json({\n      message: `تم حذف ${deletedCount} نموذج بنجاح`,\n      deletedCount\n    })\n\n  } catch (error) {\n    console.error('Error deleting AI models:', error)\n    return NextResponse.json(\n      { error: 'خطأ في حذف النماذج' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAIO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,kBAAkB,aAAa,GAAG,CAAC,wBAAwB;QACjE,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,uBAAuB;QACvB,IAAI,SAAS,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAExC,gBAAgB;QAChB,IAAI,UAAU;YACZ,SAAS,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;QACrD;QAEA,IAAI,MAAM;YACR,SAAS,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;QACjD;QAEA,IAAI,QAAQ;YACV,SAAS,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QACnD;QAEA,IAAI,CAAC,iBAAiB;YACpB,SAAS,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ;QAChD;QAEA,eAAe;QACf,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,kBAAkB,OAAO,KAAK,CAAC,YAAY;QAEjD,kBAAkB;QAClB,MAAM,QAAQ;YACZ,OAAO,OAAO,MAAM;YACpB,QAAQ,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;YAC7C,UAAU,OAAO,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ,EAAE,MAAM;YAChD,YAAY,OAAO,MAAM,CAAC,CAAC,KAAK;gBAC9B,GAAG,CAAC,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI;gBACnD,OAAO;YACT,GAAG,CAAC;YACJ,QAAQ,OAAO,MAAM,CAAC,CAAC,KAAK;gBAC1B,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;gBAC3C,OAAO;YACT,GAAG,CAAC;QACN;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,OAAO,OAAO,MAAM;YACpB;YACA;YACA,YAAY,KAAK,IAAI,CAAC,OAAO,MAAM,GAAG;YACtC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAoC,GAC7C;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAA2B,MAAM,QAAQ,IAAI;QACnD,MAAM,EACJ,IAAI,EACJ,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,MAAM,EACN,WAAW,EACX,OAAO,EACP,QAAQ,EACR,cAAc,EACd,SAAS,EACV,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,IAAI,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,SAAS,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAE1C,kCAAkC;QAClC,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;QAC9D,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4D,GACrE;gBAAE,QAAQ;YAAI;QAElB;QAEA,mCAAmC;QACnC,MAAM,gBAAgB;YACpB,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,aAAa;YACb,UAAU;YACV,eAAe;YACf,YAAY;QACd;QAEA,MAAM,gBAAgB,QAAQ,aAAa,CAAC,SAAS,IAAI;QAEzD,uCAAuC;QACvC,MAAM,YAAY,QAAQ,mBAAmB,kBAAkB,EAAE;QAEjE,uBAAuB;QACvB,MAAM,WAAoB;YACxB,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;YAC9B,MAAM;YACN;YACA,MAAM;YACN,aAAa,eAAe,CAAC,MAAM,EAAE,cAAc,iBAAiB,CAAC;YACrE,WAAW,aAAa,EAAE;YAC1B,gBAAgB,kBAAkB,EAAE;YACpC;YACA;YACA,UAAU;YACV,QAAQ;YACR,UAAU,YAAY;gBACpB,aAAa;gBACb,WAAW;gBACX,MAAM;gBACN,kBAAkB;gBAClB,iBAAiB;YACnB;YACA,OAAO;gBACL,eAAe;gBACf,aAAa;gBACb,WAAW;gBACX,YAAY,EAAE;gBACd,cAAc,EAAE;gBAChB,qBAAqB;gBACrB,aAAa;YACf;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,iCAAiC;QACjC,SAAS,mBAAmB,MAAgB;YAC1C,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAChC,EAAE,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,uBAAuB,EAAE,QAAQ,CAAC;YAEvE,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAChC,EAAE,QAAQ,CAAC,cAAc,EAAE,QAAQ,CAAC;YAEtC,MAAM,oBAAoB,OAAO,IAAI,CAAC,CAAA,IACpC,EAAE,QAAQ,CAAC;YAEb,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,IAC/B,EAAE,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC;YAEnC,MAAM,qBAAqB,OAAO,IAAI,CAAC,CAAA,IACrC,EAAE,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC;YAGrC,IAAI,oBAAoB,OAAO;YAC/B,IAAI,eAAe,OAAO;YAC1B,IAAI,eAAe,OAAO;YAC1B,IAAI,mBAAmB,OAAO;YAC9B,IAAI,cAAc,OAAO;YAEzB,OAAO,OAAO,UAAU;;QAC1B;QAEA,cAAc;QACd,OAAO,IAAI,CAAC;QACZ,wHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAE7B,aAAa;QACb,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB;QACrD,WAAW,IAAI,CAAC;YACd,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;YAC9B,SAAS,SAAS,EAAE;YACpB,MAAM;YACN,aAAa,CAAC,yBAAyB,EAAE,eAAe;YACxD,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS;QACX;QACA,wHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;QAEvC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,OAAO,CAAC,WAAW;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAC1C,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB;QACrD,IAAI,eAAe;QAEnB,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAClD,IAAI,eAAe,CAAC,GAAG;YAEvB,MAAM,QAAQ,MAAM,CAAC,WAAW;YAChC,IAAI,cAAc;YAElB,OAAQ;gBACN,KAAK;oBACH,MAAM,QAAQ,GAAG;oBACjB,MAAM,MAAM,GAAG;oBACf,cAAc,CAAC,kBAAkB,EAAE,MAAM,IAAI,EAAE;oBAC/C;gBACF,KAAK;oBACH,MAAM,QAAQ,GAAG;oBACjB,MAAM,MAAM,GAAG;oBACf,cAAc,CAAC,wBAAwB,EAAE,MAAM,IAAI,EAAE;oBACrD;gBACF,KAAK;oBACH,IAAI,UAAU;wBACZ,MAAM,QAAQ,GAAG;4BAAE,GAAG,MAAM,QAAQ;4BAAE,GAAG,QAAQ;wBAAC;wBAClD,cAAc,CAAC,0BAA0B,EAAE,MAAM,IAAI,EAAE;oBACzD;oBACA;gBACF;oBACE;YACJ;YAEA,MAAM,SAAS,GAAG,IAAI,OAAO,WAAW;YACxC,MAAM,CAAC,WAAW,GAAG;YACrB;YAEA,aAAa;YACb,WAAW,IAAI,CAAC;gBACd,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;gBAC9B,SAAS,MAAM,EAAE;gBACjB,MAAM;gBACN;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,SAAS;YACX;QACF;QAEA,gBAAgB;QAChB,wHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAC7B,wHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,CAAC,SAAS,EAAE,aAAa,YAAY,CAAC;YAC/C;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC,QAAQ,MAAM,QAAQ,EAAE;QAE1D,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwB,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAC1C,MAAM,aAAa,wHAAA,CAAA,kBAAe,CAAC,kBAAkB;QACrD,IAAI,eAAe;QAEnB,cAAc;QACd,MAAM,kBAAkB,OAAO,MAAM,CAAC,CAAA;YACpC,IAAI,SAAS,QAAQ,CAAC,MAAM,EAAE,GAAG;gBAC/B,mBAAmB;gBACnB,WAAW,IAAI,CAAC;oBACd,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;oBAC9B,SAAS,MAAM,EAAE;oBACjB,MAAM;oBACN,aAAa,CAAC,gBAAgB,EAAE,MAAM,IAAI,EAAE;oBAC5C,WAAW,IAAI,OAAO,WAAW;oBACjC,SAAS;gBACX;gBACA;gBACA,OAAO;YACT;YACA,OAAO;QACT;QAEA,gBAAgB;QAChB,wHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAC7B,wHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,CAAC,OAAO,EAAE,aAAa,YAAY,CAAC;YAC7C;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqB,GAC9B;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}