{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Loader2 } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: UserRole\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({\n  children,\n  requiredRole,\n  redirectTo = '/auth'\n}: ProtectedRouteProps) {\n  const { user, profile, loading, hasRole } = useAuth()\n  const router = useRouter()\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  useEffect(() => {\n    if (mounted && !loading) {\n      if (!user) {\n        router.push(redirectTo)\n        return\n      }\n\n      if (requiredRole && !hasRole(requiredRole)) {\n        router.push('/unauthorized')\n        return\n      }\n    }\n  }, [mounted, user, profile, loading, requiredRole, hasRole, router, redirectTo])\n\n  // Show loading until mounted and auth is resolved\n  if (!mounted || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\n            <Loader2 className=\"h-8 w-8 animate-spin text-blue-600 mb-4\" />\n            <p className=\"text-gray-600 dark:text-gray-300\">جاري التحميل...</p>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect\n  }\n\n  if (requiredRole && !hasRole(requiredRole)) {\n    return null // Will redirect\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AAeO,SAAS,eAAe,EAC7B,QAAQ,EACR,YAAY,EACZ,aAAa,OAAO,EACA;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,CAAC,SAAS;YACvB,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;gBAC1C,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF,GAAG;QAAC;QAAS;QAAM;QAAS;QAAS;QAAc;QAAS;QAAQ;KAAW;IAE/E,kDAAkD;IAClD,IAAI,CAAC,WAAW,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;QAC1C,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AdminQuickNav.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport {\n  Plus,\n  Menu,\n  Package,\n  Download,\n  FileText,\n  Users,\n  Settings\n} from 'lucide-react'\n\ninterface QuickNavItem {\n  label: string\n  href?: string\n  icon: React.ReactNode\n  variant?: 'default' | 'outline' | 'secondary'\n  disabled?: boolean\n}\n\nconst quickNavItems: QuickNavItem[] = [\n  {\n    label: 'إضافة صفحة',\n    href: '/dashboard/admin/pages-management',\n    icon: <Plus className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline'\n  },\n  {\n    label: 'تحرير القائمة',\n    href: '/dashboard/admin/menu-management',\n    icon: <Menu className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline'\n  },\n  {\n    label: 'إدارة المنتجات',\n    href: '/dashboard/admin/products',\n    icon: <Package className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline'\n  },\n  {\n    label: 'تصدير التقارير',\n    icon: <Download className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline',\n    disabled: true\n  }\n]\n\nexport function AdminQuickNav() {\n  return (\n    <div className=\"flex gap-3 flex-wrap\">\n      {quickNavItems.map((item, index) => {\n        const ButtonComponent = (\n          <Button \n            key={index}\n            variant={item.variant || 'outline'} \n            size=\"sm\"\n            disabled={item.disabled}\n            className=\"arabic-text\"\n          >\n            {item.icon}\n            {item.label}\n          </Button>\n        )\n\n        return item.href && !item.disabled ? (\n          <Link key={index} href={item.href}>\n            {ButtonComponent}\n          </Link>\n        ) : (\n          ButtonComponent\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAsBA,MAAM,gBAAgC;IACpC;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,SAAS;IACX;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,SAAS;IACX;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,SAAS;IACX;IACA;QACE,OAAO;QACP,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,SAAS;QACT,UAAU;IACZ;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC,MAAM;YACxB,MAAM,gCACJ,8OAAC,kIAAA,CAAA,SAAM;gBAEL,SAAS,KAAK,OAAO,IAAI;gBACzB,MAAK;gBACL,UAAU,KAAK,QAAQ;gBACvB,WAAU;;oBAET,KAAK,IAAI;oBACT,KAAK,KAAK;;eAPN;;;;;YAWT,OAAO,KAAK,IAAI,IAAI,CAAC,KAAK,QAAQ,iBAChC,8OAAC,4JAAA,CAAA,UAAI;gBAAa,MAAM,KAAK,IAAI;0BAC9B;eADQ;;;;uBAIX;QAEJ;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AdminDashboardHeader.tsx"], "sourcesContent": ["\"use client\"\n\nimport { AdminQuickNav } from './AdminQuickNav'\nimport { Badge } from '@/components/ui/badge'\nimport { Bell, Crown } from 'lucide-react'\n\ninterface AdminDashboardHeaderProps {\n  alertsCount?: number\n}\n\nexport function AdminDashboardHeader({ alertsCount = 0 }: AdminDashboardHeaderProps) {\n  return (\n    <div className=\"mb-8\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text flex items-center gap-2\">\n            <Crown className=\"h-8 w-8 text-yellow-500\" />\n            لوحة تحكم الإدارة\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n            إدارة شاملة للمنصة والمستخدمين والطلبات\n          </p>\n          {alertsCount > 0 && (\n            <div className=\"flex items-center gap-2 mt-2\">\n              <Bell className=\"h-4 w-4 text-amber-500\" />\n              <Badge variant=\"destructive\" className=\"text-xs\">\n                {alertsCount} تنبيه جديد\n              </Badge>\n            </div>\n          )}\n        </div>\n        \n        <AdminQuickNav />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAUO,SAAS,qBAAqB,EAAE,cAAc,CAAC,EAA6B;IACjF,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;sCAG/C,8OAAC;4BAAE,WAAU;sCAAoD;;;;;;wBAGhE,cAAc,mBACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;;wCACpC;wCAAY;;;;;;;;;;;;;;;;;;;8BAMrB,8OAAC,4IAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,kKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,mKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,mKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qXACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AIModelCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { AIModel } from '@/types/ai-models'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Switch } from '@/components/ui/switch'\nimport { \n  Brain, \n  Settings, \n  TestTube, \n  Activity, \n  DollarSign, \n  Clock, \n  CheckCircle, \n  XCircle, \n  AlertTriangle,\n  MoreHorizontal,\n  Edit,\n  Trash2,\n  BarChart3,\n  Zap\n} from 'lucide-react'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from '@/components/ui/tooltip'\n\ninterface AIModelCardProps {\n  model: AIModel\n  onEdit?: (model: AIModel) => void\n  onDelete?: (model: AIModel) => void\n  onTest?: (model: AIModel) => void\n  onToggleActive?: (model: AIModel, isActive: boolean) => void\n  onViewUsage?: (model: AIModel) => void\n  onSettings?: (model: AIModel) => void\n}\n\nexport function AIModelCard({\n  model,\n  onEdit,\n  onDelete,\n  onTest,\n  onToggleActive,\n  onViewUsage,\n  onSettings\n}: AIModelCardProps) {\n  const [isLoading, setIsLoading] = useState(false)\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active': return 'bg-green-500'\n      case 'inactive': return 'bg-gray-500'\n      case 'error': return 'bg-red-500'\n      case 'testing': return 'bg-yellow-500'\n      default: return 'bg-gray-500'\n    }\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'active': return <CheckCircle className=\"h-4 w-4\" />\n      case 'inactive': return <XCircle className=\"h-4 w-4\" />\n      case 'error': return <AlertTriangle className=\"h-4 w-4\" />\n      case 'testing': return <TestTube className=\"h-4 w-4\" />\n      default: return <XCircle className=\"h-4 w-4\" />\n    }\n  }\n\n  const getProviderColor = (provider: string) => {\n    switch (provider) {\n      case 'openai': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n      case 'anthropic': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'\n      case 'google': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\n      case 'meta': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'\n      case 'stability': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300'\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n    }\n  }\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'text': return '📝'\n      case 'image': return '🖼️'\n      case 'audio': return '🎵'\n      case 'multimodal': return '🔄'\n      case 'code': return '💻'\n      case 'embedding': return '🔗'\n      default: return '🤖'\n    }\n  }\n\n  const handleToggleActive = async (checked: boolean) => {\n    setIsLoading(true)\n    try {\n      await onToggleActive?.(model, checked)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleTest = async () => {\n    setIsLoading(true)\n    try {\n      await onTest?.(model)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('ar-MA', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2\n    }).format(amount)\n  }\n\n  const formatNumber = (num: number) => {\n    return new Intl.NumberFormat('ar-MA').format(num)\n  }\n\n  return (\n    <Card className=\"relative overflow-hidden\">\n      {/* شريط الحالة */}\n      <div className={`absolute top-0 left-0 right-0 h-1 ${getStatusColor(model.status)}`} />\n      \n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"text-2xl\">{model.icon || getTypeIcon(model.type)}</div>\n            <div>\n              <CardTitle className=\"text-lg arabic-text\">{model.name}</CardTitle>\n              <div className=\"flex items-center gap-2 mt-1\">\n                <Badge variant=\"secondary\" className={getProviderColor(model.provider)}>\n                  {model.provider}\n                </Badge>\n                <Badge variant=\"outline\">\n                  {getTypeIcon(model.type)} {model.type}\n                </Badge>\n                <div className=\"flex items-center gap-1\">\n                  {getStatusIcon(model.status)}\n                  <span className=\"text-sm text-muted-foreground\">{model.status}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <div className=\"flex items-center gap-2\">\n                    <Switch\n                      checked={model.isActive}\n                      onCheckedChange={handleToggleActive}\n                      disabled={isLoading}\n                    />\n                  </div>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>{model.isActive ? 'إلغاء التفعيل' : 'تفعيل'}</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" size=\"sm\">\n                  <MoreHorizontal className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <DropdownMenuItem onClick={() => onEdit?.(model)}>\n                  <Edit className=\"h-4 w-4 mr-2\" />\n                  تحرير\n                </DropdownMenuItem>\n                <DropdownMenuItem onClick={() => onSettings?.(model)}>\n                  <Settings className=\"h-4 w-4 mr-2\" />\n                  الإعدادات\n                </DropdownMenuItem>\n                <DropdownMenuItem onClick={handleTest} disabled={!model.isActive || isLoading}>\n                  <TestTube className=\"h-4 w-4 mr-2\" />\n                  اختبار\n                </DropdownMenuItem>\n                <DropdownMenuItem onClick={() => onViewUsage?.(model)}>\n                  <BarChart3 className=\"h-4 w-4 mr-2\" />\n                  الإحصائيات\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem \n                  onClick={() => onDelete?.(model)}\n                  className=\"text-destructive\"\n                >\n                  <Trash2 className=\"h-4 w-4 mr-2\" />\n                  حذف\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n        \n        {model.description && (\n          <p className=\"text-sm text-muted-foreground arabic-text mt-2\">\n            {model.description}\n          </p>\n        )}\n      </CardHeader>\n\n      <CardContent className=\"space-y-4\">\n        {/* النماذج الفرعية */}\n        {model.subModels.length > 0 && (\n          <div>\n            <h4 className=\"text-sm font-medium arabic-text mb-2\">النماذج الفرعية</h4>\n            <div className=\"flex flex-wrap gap-1\">\n              {model.subModels.slice(0, 3).map((subModel) => (\n                <Badge \n                  key={subModel.id} \n                  variant={subModel.isActive ? \"default\" : \"secondary\"}\n                  className=\"text-xs\"\n                >\n                  {subModel.name}\n                  {subModel.isDefault && <span className=\"mr-1\">⭐</span>}\n                </Badge>\n              ))}\n              {model.subModels.length > 3 && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  +{model.subModels.length - 3}\n                </Badge>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* إحصائيات الاستخدام */}\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Activity className=\"h-4 w-4 text-blue-500\" />\n              <span className=\"text-sm text-muted-foreground\">الطلبات</span>\n            </div>\n            <p className=\"text-lg font-semibold\">\n              {formatNumber(model.usage.totalRequests)}\n            </p>\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <DollarSign className=\"h-4 w-4 text-green-500\" />\n              <span className=\"text-sm text-muted-foreground\">التكلفة</span>\n            </div>\n            <p className=\"text-lg font-semibold\">\n              {formatCurrency(model.usage.totalCost)}\n            </p>\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Clock className=\"h-4 w-4 text-orange-500\" />\n              <span className=\"text-sm text-muted-foreground\">متوسط الاستجابة</span>\n            </div>\n            <p className=\"text-lg font-semibold\">\n              {model.usage.averageResponseTime}ms\n            </p>\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Zap className=\"h-4 w-4 text-purple-500\" />\n              <span className=\"text-sm text-muted-foreground\">معدل النجاح</span>\n            </div>\n            <p className=\"text-lg font-semibold\">\n              {model.usage.successRate}%\n            </p>\n          </div>\n        </div>\n\n        {/* آخر اختبار */}\n        {model.lastTestedAt && model.testResult && (\n          <div className=\"border-t pt-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-muted-foreground\">آخر اختبار</span>\n              <div className=\"flex items-center gap-2\">\n                {model.testResult.success ? (\n                  <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                ) : (\n                  <XCircle className=\"h-4 w-4 text-red-500\" />\n                )}\n                <span className=\"text-sm\">\n                  {model.testResult.responseTime}ms\n                </span>\n              </div>\n            </div>\n            <p className=\"text-xs text-muted-foreground mt-1\">\n              {new Date(model.lastTestedAt).toLocaleDateString('ar-MA')}\n            </p>\n          </div>\n        )}\n\n        {/* أزرار الإجراءات */}\n        <div className=\"flex gap-2 pt-2\">\n          <Button \n            size=\"sm\" \n            variant=\"outline\" \n            onClick={handleTest}\n            disabled={!model.isActive || isLoading}\n            className=\"flex-1\"\n          >\n            <TestTube className=\"h-4 w-4 mr-2\" />\n            اختبار\n          </Button>\n          <Button \n            size=\"sm\" \n            variant=\"outline\" \n            onClick={() => onViewUsage?.(model)}\n            className=\"flex-1\"\n          >\n            <BarChart3 className=\"h-4 w-4 mr-2\" />\n            الإحصائيات\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAOA;AA/BA;;;;;;;;;;AAgDO,SAAS,YAAY,EAC1B,KAAK,EACL,MAAM,EACN,QAAQ,EACR,MAAM,EACN,cAAc,EACd,WAAW,EACX,UAAU,EACO;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAY,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAS,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAC9C,KAAK;gBAAW,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC3C;gBAAS,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QACrC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,aAAa;QACb,IAAI;YACF,MAAM,iBAAiB,OAAO;QAChC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;QACb,IAAI;YACF,MAAM,SAAS;QACjB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAC/C;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BAEd,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,eAAe,MAAM,MAAM,GAAG;;;;;;0BAEnF,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY,MAAM,IAAI,IAAI,YAAY,MAAM,IAAI;;;;;;kDAC/D,8OAAC;;0DACC,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAuB,MAAM,IAAI;;;;;;0DACtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAW,iBAAiB,MAAM,QAAQ;kEAClE,MAAM,QAAQ;;;;;;kEAEjB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;;4DACZ,YAAY,MAAM,IAAI;4DAAE;4DAAE,MAAM,IAAI;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;4DACZ,cAAc,MAAM,MAAM;0EAC3B,8OAAC;gEAAK,WAAU;0EAAiC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMrE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,MAAM,QAAQ;4DACvB,iBAAiB;4DACjB,UAAU;;;;;;;;;;;;;;;;8DAIhB,8OAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;kEAAG,MAAM,QAAQ,GAAG,kBAAkB;;;;;;;;;;;;;;;;;;;;;;kDAK7C,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;8DAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;;kEACzB,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,SAAS;;0EACxC,8OAAC,2MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,aAAa;;0EAC5C,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,SAAS;wDAAY,UAAU,CAAC,MAAM,QAAQ,IAAI;;0EAClE,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,cAAc;;0EAC7C,8OAAC,kNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGxC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kEACtB,8OAAC,4IAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,WAAW;wDAC1B,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ5C,MAAM,WAAW,kBAChB,8OAAC;wBAAE,WAAU;kCACV,MAAM,WAAW;;;;;;;;;;;;0BAKxB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAChC,8OAAC,iIAAA,CAAA,QAAK;4CAEJ,SAAS,SAAS,QAAQ,GAAG,YAAY;4CACzC,WAAU;;gDAET,SAAS,IAAI;gDACb,SAAS,SAAS,kBAAI,8OAAC;oDAAK,WAAU;8DAAO;;;;;;;2CALzC,SAAS,EAAE;;;;;oCAQnB,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAU;4CACzC,MAAM,SAAS,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;kCAQrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,8OAAC;wCAAE,WAAU;kDACV,aAAa,MAAM,KAAK,CAAC,aAAa;;;;;;;;;;;;0CAI3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,8OAAC;wCAAE,WAAU;kDACV,eAAe,MAAM,KAAK,CAAC,SAAS;;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,8OAAC;wCAAE,WAAU;;4CACV,MAAM,KAAK,CAAC,mBAAmB;4CAAC;;;;;;;;;;;;;0CAIrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,8OAAC;wCAAE,WAAU;;4CACV,MAAM,KAAK,CAAC,WAAW;4CAAC;;;;;;;;;;;;;;;;;;;oBAM9B,MAAM,YAAY,IAAI,MAAM,UAAU,kBACrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgC;;;;;;kDAChD,8OAAC;wCAAI,WAAU;;4CACZ,MAAM,UAAU,CAAC,OAAO,iBACvB,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DAErB,8OAAC;gDAAK,WAAU;;oDACb,MAAM,UAAU,CAAC,YAAY;oDAAC;;;;;;;;;;;;;;;;;;;0CAIrC,8OAAC;gCAAE,WAAU;0CACV,IAAI,KAAK,MAAM,YAAY,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kCAMvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,CAAC,MAAM,QAAQ,IAAI;gCAC7B,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS,IAAM,cAAc;gCAC7B,WAAU;;kDAEV,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD", "debugId": null}}, {"offset": {"line": 1556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Slider({\n  className,\n  defaultValue,\n  value,\n  min = 0,\n  max = 100,\n  ...props\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\n  const _values = React.useMemo(\n    () =>\n      Array.isArray(value)\n        ? value\n        : Array.isArray(defaultValue)\n          ? defaultValue\n          : [min, max],\n    [value, defaultValue, min, max]\n  )\n\n  return (\n    <SliderPrimitive.Root\n      data-slot=\"slider\"\n      defaultValue={defaultValue}\n      value={value}\n      min={min}\n      max={max}\n      className={cn(\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    >\n      <SliderPrimitive.Track\n        data-slot=\"slider-track\"\n        className={cn(\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\n        )}\n      >\n        <SliderPrimitive.Range\n          data-slot=\"slider-range\"\n          className={cn(\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\n          )}\n        />\n      </SliderPrimitive.Track>\n      {Array.from({ length: _values.length }, (_, index) => (\n        <SliderPrimitive.Thumb\n          data-slot=\"slider-thumb\"\n          key={index}\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\n        />\n      ))}\n    </SliderPrimitive.Root>\n  )\n}\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;IAClD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC1B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;YAAC;YAAK;SAAI,EAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2079, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AIModelForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { AIModel, AIProvider, ModelType, CreateModelRequest, UpdateModelRequest } from '@/types/ai-models'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Switch } from '@/components/ui/switch'\nimport { Slider } from '@/components/ui/slider'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Tabs,\n  TabsContent,\n  TabsList,\n  TabsTrigger,\n} from '@/components/ui/tabs'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Eye, EyeOff, TestTube, Save, X } from 'lucide-react'\n\ninterface AIModelFormProps {\n  model?: AIModel\n  isOpen: boolean\n  onClose: () => void\n  onSave: (data: CreateModelRequest | UpdateModelRequest) => Promise<void>\n  onTest?: (data: any) => Promise<void>\n}\n\nconst PROVIDERS: { value: AIProvider; label: string; description: string }[] = [\n  { value: 'openai', label: 'OpenAI', description: 'GPT, DALL-E, Whisper' },\n  { value: 'anthropic', label: 'Anthropic', description: 'Claude Models' },\n  { value: 'google', label: 'Google', description: 'Gemini, PaLM' },\n  { value: 'meta', label: 'Meta', description: 'LLaMA Models' },\n  { value: 'stability', label: 'Stability AI', description: 'Stable Diffusion' },\n  { value: 'cohere', label: 'Cohere', description: 'Command Models' },\n  { value: 'huggingface', label: 'Hugging Face', description: 'Open Source Models' },\n  { value: 'deepseek', label: 'DeepSeek', description: 'DeepSeek Models' }\n]\n\nconst MODEL_TYPES: { value: ModelType; label: string; icon: string }[] = [\n  { value: 'text', label: 'نص', icon: '📝' },\n  { value: 'image', label: 'صورة', icon: '🖼️' },\n  { value: 'audio', label: 'صوت', icon: '🎵' },\n  { value: 'multimodal', label: 'متعدد الوسائط', icon: '🔄' },\n  { value: 'code', label: 'كود', icon: '💻' },\n  { value: 'embedding', label: 'تضمين', icon: '🔗' }\n]\n\nexport function AIModelForm({ model, isOpen, onClose, onSave, onTest }: AIModelFormProps) {\n  const [formData, setFormData] = useState<any>({\n    name: '',\n    provider: 'openai' as AIProvider,\n    type: 'text' as ModelType,\n    description: '',\n    apiKey: '',\n    apiEndpoint: '',\n    isActive: true,\n    settings: {\n      temperature: 0.7,\n      maxTokens: 2048,\n      topP: 1,\n      frequencyPenalty: 0,\n      presencePenalty: 0,\n      systemPrompt: ''\n    }\n  })\n  \n  const [showApiKey, setShowApiKey] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [testPrompt, setTestPrompt] = useState('مرحباً، كيف يمكنني مساعدتك؟')\n\n  useEffect(() => {\n    if (model) {\n      setFormData({\n        name: model.name,\n        provider: model.provider,\n        type: model.type,\n        description: model.description || '',\n        apiKey: model.apiKey || '',\n        apiEndpoint: model.apiEndpoint || '',\n        isActive: model.isActive,\n        settings: { ...model.settings }\n      })\n    } else {\n      // إعادة تعيين النموذج للإضافة الجديدة\n      setFormData({\n        name: '',\n        provider: 'openai' as AIProvider,\n        type: 'text' as ModelType,\n        description: '',\n        apiKey: '',\n        apiEndpoint: '',\n        isActive: true,\n        settings: {\n          temperature: 0.7,\n          maxTokens: 2048,\n          topP: 1,\n          frequencyPenalty: 0,\n          presencePenalty: 0,\n          systemPrompt: ''\n        }\n      })\n    }\n  }, [model, isOpen])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    \n    try {\n      await onSave(formData)\n      onClose()\n    } catch (error) {\n      console.error('Error saving model:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleTest = async () => {\n    if (!onTest) return\n    \n    setIsLoading(true)\n    try {\n      await onTest({\n        modelId: model?.id,\n        prompt: testPrompt,\n        settings: formData.settings\n      })\n    } catch (error) {\n      console.error('Error testing model:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const updateFormData = (field: string, value: any) => {\n    setFormData((prev: any) => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  const updateSettings = (field: string, value: any) => {\n    setFormData((prev: any) => ({\n      ...prev,\n      settings: {\n        ...prev.settings,\n        [field]: value\n      }\n    }))\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"arabic-text\">\n            {model ? 'تحرير نموذج الذكاء الاصطناعي' : 'إضافة نموذج جديد'}\n          </DialogTitle>\n          <DialogDescription>\n            {model \n              ? 'قم بتحديث إعدادات النموذج وخصائصه'\n              : 'أضف نموذج ذكاء اصطناعي جديد للمنصة'\n            }\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit}>\n          <Tabs defaultValue=\"basic\" className=\"w-full\">\n            <TabsList className=\"grid w-full grid-cols-3\">\n              <TabsTrigger value=\"basic\">المعلومات الأساسية</TabsTrigger>\n              <TabsTrigger value=\"settings\">الإعدادات</TabsTrigger>\n              <TabsTrigger value=\"test\">الاختبار</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"basic\" className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"name\">اسم النموذج *</Label>\n                  <Input\n                    id=\"name\"\n                    value={formData.name}\n                    onChange={(e) => updateFormData('name', e.target.value)}\n                    placeholder=\"مثال: GPT-4 Turbo\"\n                    required\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"provider\">مقدم الخدمة *</Label>\n                  <Select \n                    value={formData.provider} \n                    onValueChange={(value) => updateFormData('provider', value)}\n                  >\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {PROVIDERS.map((provider) => (\n                        <SelectItem key={provider.value} value={provider.value}>\n                          <div className=\"flex flex-col\">\n                            <span>{provider.label}</span>\n                            <span className=\"text-xs text-muted-foreground\">\n                              {provider.description}\n                            </span>\n                          </div>\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"type\">نوع النموذج *</Label>\n                  <Select \n                    value={formData.type} \n                    onValueChange={(value) => updateFormData('type', value)}\n                  >\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {MODEL_TYPES.map((type) => (\n                        <SelectItem key={type.value} value={type.value}>\n                          <div className=\"flex items-center gap-2\">\n                            <span>{type.icon}</span>\n                            <span>{type.label}</span>\n                          </div>\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"isActive\" className=\"flex items-center gap-2\">\n                    <Switch\n                      id=\"isActive\"\n                      checked={formData.isActive}\n                      onCheckedChange={(checked) => updateFormData('isActive', checked)}\n                    />\n                    نشط\n                  </Label>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\">الوصف</Label>\n                <Textarea\n                  id=\"description\"\n                  value={formData.description}\n                  onChange={(e) => updateFormData('description', e.target.value)}\n                  placeholder=\"وصف مختصر للنموذج وقدراته\"\n                  rows={3}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"apiKey\">مفتاح API</Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"apiKey\"\n                    type={showApiKey ? 'text' : 'password'}\n                    value={formData.apiKey}\n                    onChange={(e) => updateFormData('apiKey', e.target.value)}\n                    placeholder=\"أدخل مفتاح API\"\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"absolute left-2 top-1/2 -translate-y-1/2\"\n                    onClick={() => setShowApiKey(!showApiKey)}\n                  >\n                    {showApiKey ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                  </Button>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"apiEndpoint\">نقطة النهاية (اختياري)</Label>\n                <Input\n                  id=\"apiEndpoint\"\n                  value={formData.apiEndpoint}\n                  onChange={(e) => updateFormData('apiEndpoint', e.target.value)}\n                  placeholder=\"https://api.example.com/v1\"\n                />\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"settings\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">إعدادات التوليد</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div className=\"space-y-2\">\n                    <Label>درجة الحرارة: {formData.settings.temperature}</Label>\n                    <Slider\n                      value={[formData.settings.temperature]}\n                      onValueChange={([value]) => updateSettings('temperature', value)}\n                      max={2}\n                      min={0}\n                      step={0.1}\n                      className=\"w-full\"\n                    />\n                    <p className=\"text-xs text-muted-foreground\">\n                      قيم أعلى تعني إجابات أكثر إبداعاً وعشوائية\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label>الحد الأقصى للرموز: {formData.settings.maxTokens}</Label>\n                    <Slider\n                      value={[formData.settings.maxTokens]}\n                      onValueChange={([value]) => updateSettings('maxTokens', value)}\n                      max={8192}\n                      min={1}\n                      step={1}\n                      className=\"w-full\"\n                    />\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label>Top P: {formData.settings.topP}</Label>\n                    <Slider\n                      value={[formData.settings.topP]}\n                      onValueChange={([value]) => updateSettings('topP', value)}\n                      max={1}\n                      min={0}\n                      step={0.01}\n                      className=\"w-full\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label>عقوبة التكرار: {formData.settings.frequencyPenalty}</Label>\n                      <Slider\n                        value={[formData.settings.frequencyPenalty]}\n                        onValueChange={([value]) => updateSettings('frequencyPenalty', value)}\n                        max={2}\n                        min={0}\n                        step={0.1}\n                        className=\"w-full\"\n                      />\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label>عقوبة الحضور: {formData.settings.presencePenalty}</Label>\n                      <Slider\n                        value={[formData.settings.presencePenalty]}\n                        onValueChange={([value]) => updateSettings('presencePenalty', value)}\n                        max={2}\n                        min={0}\n                        step={0.1}\n                        className=\"w-full\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"systemPrompt\">الرسالة النظامية</Label>\n                    <Textarea\n                      id=\"systemPrompt\"\n                      value={formData.settings.systemPrompt}\n                      onChange={(e) => updateSettings('systemPrompt', e.target.value)}\n                      placeholder=\"أنت مساعد ذكي مفيد ومهذب...\"\n                      rows={4}\n                    />\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"test\" className=\"space-y-4\">\n              {model && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"text-lg\">اختبار النموذج</CardTitle>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"testPrompt\">النص التجريبي</Label>\n                      <Textarea\n                        id=\"testPrompt\"\n                        value={testPrompt}\n                        onChange={(e) => setTestPrompt(e.target.value)}\n                        placeholder=\"أدخل نص لاختبار النموذج\"\n                        rows={3}\n                      />\n                    </div>\n\n                    <Button \n                      type=\"button\" \n                      onClick={handleTest}\n                      disabled={isLoading || !testPrompt.trim()}\n                      className=\"w-full\"\n                    >\n                      <TestTube className=\"h-4 w-4 mr-2\" />\n                      {isLoading ? 'جاري الاختبار...' : 'اختبار النموذج'}\n                    </Button>\n\n                    {model.testResult && (\n                      <div className=\"mt-4 p-4 border rounded-lg\">\n                        <div className=\"flex items-center gap-2 mb-2\">\n                          <Badge variant={model.testResult.success ? \"default\" : \"destructive\"}>\n                            {model.testResult.success ? 'نجح' : 'فشل'}\n                          </Badge>\n                          <span className=\"text-sm text-muted-foreground\">\n                            {model.testResult.responseTime}ms\n                          </span>\n                        </div>\n                        {model.testResult.error && (\n                          <p className=\"text-sm text-destructive\">{model.testResult.error}</p>\n                        )}\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n              )}\n            </TabsContent>\n          </Tabs>\n\n          <DialogFooter className=\"mt-6\">\n            <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n              <X className=\"h-4 w-4 mr-2\" />\n              إلغاء\n            </Button>\n            <Button type=\"submit\" disabled={isLoading}>\n              <Save className=\"h-4 w-4 mr-2\" />\n              {isLoading ? 'جاري الحفظ...' : 'حفظ'}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAQA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAjCA;;;;;;;;;;;;;;;AA2CA,MAAM,YAAyE;IAC7E;QAAE,OAAO;QAAU,OAAO;QAAU,aAAa;IAAuB;IACxE;QAAE,OAAO;QAAa,OAAO;QAAa,aAAa;IAAgB;IACvE;QAAE,OAAO;QAAU,OAAO;QAAU,aAAa;IAAe;IAChE;QAAE,OAAO;QAAQ,OAAO;QAAQ,aAAa;IAAe;IAC5D;QAAE,OAAO;QAAa,OAAO;QAAgB,aAAa;IAAmB;IAC7E;QAAE,OAAO;QAAU,OAAO;QAAU,aAAa;IAAiB;IAClE;QAAE,OAAO;QAAe,OAAO;QAAgB,aAAa;IAAqB;IACjF;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAAkB;CACxE;AAED,MAAM,cAAmE;IACvE;QAAE,OAAO;QAAQ,OAAO;QAAM,MAAM;IAAK;IACzC;QAAE,OAAO;QAAS,OAAO;QAAQ,MAAM;IAAM;IAC7C;QAAE,OAAO;QAAS,OAAO;QAAO,MAAM;IAAK;IAC3C;QAAE,OAAO;QAAc,OAAO;QAAiB,MAAM;IAAK;IAC1D;QAAE,OAAO;QAAQ,OAAO;QAAO,MAAM;IAAK;IAC1C;QAAE,OAAO;QAAa,OAAO;QAAS,MAAM;IAAK;CAClD;AAEM,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAoB;IACtF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;QAC5C,MAAM;QACN,UAAU;QACV,MAAM;QACN,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;YACR,aAAa;YACb,WAAW;YACX,MAAM;YACN,kBAAkB;YAClB,iBAAiB;YACjB,cAAc;QAChB;IACF;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,YAAY;gBACV,MAAM,MAAM,IAAI;gBAChB,UAAU,MAAM,QAAQ;gBACxB,MAAM,MAAM,IAAI;gBAChB,aAAa,MAAM,WAAW,IAAI;gBAClC,QAAQ,MAAM,MAAM,IAAI;gBACxB,aAAa,MAAM,WAAW,IAAI;gBAClC,UAAU,MAAM,QAAQ;gBACxB,UAAU;oBAAE,GAAG,MAAM,QAAQ;gBAAC;YAChC;QACF,OAAO;YACL,sCAAsC;YACtC,YAAY;gBACV,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,aAAa;gBACb,UAAU;gBACV,UAAU;oBACR,aAAa;oBACb,WAAW;oBACX,MAAM;oBACN,kBAAkB;oBAClB,iBAAiB;oBACjB,cAAc;gBAChB;YACF;QACF;IACF,GAAG;QAAC;QAAO;KAAO;IAElB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,OAAO;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ;QAEb,aAAa;QACb,IAAI;YACF,MAAM,OAAO;gBACX,SAAS,OAAO;gBAChB,QAAQ;gBACR,UAAU,SAAS,QAAQ;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,YAAY,CAAC,OAAc,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,YAAY,CAAC,OAAc,CAAC;gBAC1B,GAAG,IAAI;gBACP,UAAU;oBACR,GAAG,KAAK,QAAQ;oBAChB,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;IACH;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,QAAQ,iCAAiC;;;;;;sCAE5C,8OAAC,kIAAA,CAAA,oBAAiB;sCACf,QACG,sCACA;;;;;;;;;;;;8BAKR,8OAAC;oBAAK,UAAU;;sCACd,8OAAC,gIAAA,CAAA,OAAI;4BAAC,cAAa;4BAAQ,WAAU;;8CACnC,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAQ;;;;;;sDAC3B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;sDAC9B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAO;;;;;;;;;;;;8CAG5B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAO;;;;;;sEACtB,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,eAAe,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACtD,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,QAAQ;4DACxB,eAAe,CAAC,QAAU,eAAe,YAAY;;8EAErD,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,8OAAC,kIAAA,CAAA,gBAAa;8EACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;4EAAsB,OAAO,SAAS,KAAK;sFACpD,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;kGAAM,SAAS,KAAK;;;;;;kGACrB,8OAAC;wFAAK,WAAU;kGACb,SAAS,WAAW;;;;;;;;;;;;2EAJV,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;8DAavC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAO;;;;;;sEACtB,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,IAAI;4DACpB,eAAe,CAAC,QAAU,eAAe,QAAQ;;8EAEjD,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,8OAAC,kIAAA,CAAA,gBAAa;8EACX,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,kIAAA,CAAA,aAAU;4EAAkB,OAAO,KAAK,KAAK;sFAC5C,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;kGAAM,KAAK,IAAI;;;;;;kGAChB,8OAAC;kGAAM,KAAK,KAAK;;;;;;;;;;;;2EAHJ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;8DAWnC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;;0EAClC,8OAAC,kIAAA,CAAA,SAAM;gEACL,IAAG;gEACH,SAAS,SAAS,QAAQ;gEAC1B,iBAAiB,CAAC,UAAY,eAAe,YAAY;;;;;;4DACzD;;;;;;;;;;;;;;;;;;sDAMR,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,eAAe,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC7D,aAAY;oDACZ,MAAM;;;;;;;;;;;;sDAIV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAM,aAAa,SAAS;4DAC5B,OAAO,SAAS,MAAM;4DACtB,UAAU,CAAC,IAAM,eAAe,UAAU,EAAE,MAAM,CAAC,KAAK;4DACxD,aAAY;;;;;;sEAEd,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,cAAc,CAAC;sEAE7B,2BAAa,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAAe,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAKpE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,eAAe,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC7D,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAe,SAAS,QAAQ,CAAC,WAAW;;;;;;;0EACnD,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,SAAS,QAAQ,CAAC,WAAW;iEAAC;gEACtC,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,eAAe;gEAC1D,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;0EAEZ,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAK/C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAqB,SAAS,QAAQ,CAAC,SAAS;;;;;;;0EACvD,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,SAAS,QAAQ,CAAC,SAAS;iEAAC;gEACpC,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,aAAa;gEACxD,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAId,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAQ,SAAS,QAAQ,CAAC,IAAI;;;;;;;0EACrC,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,SAAS,QAAQ,CAAC,IAAI;iEAAC;gEAC/B,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,QAAQ;gEACnD,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAId,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;;4EAAC;4EAAgB,SAAS,QAAQ,CAAC,gBAAgB;;;;;;;kFACzD,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO;4EAAC,SAAS,QAAQ,CAAC,gBAAgB;yEAAC;wEAC3C,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,oBAAoB;wEAC/D,KAAK;wEACL,KAAK;wEACL,MAAM;wEACN,WAAU;;;;;;;;;;;;0EAId,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;;4EAAC;4EAAe,SAAS,QAAQ,CAAC,eAAe;;;;;;;kFACvD,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO;4EAAC,SAAS,QAAQ,CAAC,eAAe;yEAAC;wEAC1C,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,mBAAmB;wEAC9D,KAAK;wEACL,KAAK;wEACL,MAAM;wEACN,WAAU;;;;;;;;;;;;;;;;;;kEAKhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAe;;;;;;0EAC9B,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,OAAO,SAAS,QAAQ,CAAC,YAAY;gEACrC,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAC9D,aAAY;gEACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOhB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CACjC,uBACC,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAa;;;;;;0EAC5B,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,aAAY;gEACZ,MAAM;;;;;;;;;;;;kEAIV,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS;wDACT,UAAU,aAAa,CAAC,WAAW,IAAI;wDACvC,WAAU;;0EAEV,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,YAAY,qBAAqB;;;;;;;oDAGnC,MAAM,UAAU,kBACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAS,MAAM,UAAU,CAAC,OAAO,GAAG,YAAY;kFACpD,MAAM,UAAU,CAAC,OAAO,GAAG,QAAQ;;;;;;kFAEtC,8OAAC;wEAAK,WAAU;;4EACb,MAAM,UAAU,CAAC,YAAY;4EAAC;;;;;;;;;;;;;4DAGlC,MAAM,UAAU,CAAC,KAAK,kBACrB,8OAAC;gEAAE,WAAU;0EAA4B,MAAM,UAAU,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU/E,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;;sDAC/C,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGhC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;;sDAC9B,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 3219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,2KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,2KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,2KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,2KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,2KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/dashboard/admin/ai-models/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { AdminDashboardHeader } from '@/components/admin/AdminDashboardHeader'\nimport { AIModelCard } from '@/components/admin/AIModelCard'\nimport { AIModelForm } from '@/components/admin/AIModelForm'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog'\nimport { toast } from 'sonner'\nimport { \n  Brain, \n  Plus, \n  Search, \n  Filter, \n  BarChart3, \n  Activity, \n  DollarSign, \n  Zap,\n  RefreshCw,\n  Settings,\n  TestTube\n} from 'lucide-react'\nimport { AIModel, CreateModelRequest, UpdateModelRequest } from '@/types/ai-models'\n\nexport default function AIModelsPage() {\n  const { user, profile } = useAuth()\n  const [models, setModels] = useState<AIModel[]>([])\n  const [filteredModels, setFilteredModels] = useState<AIModel[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedProvider, setSelectedProvider] = useState<string>('all')\n  const [selectedType, setSelectedType] = useState<string>('all')\n  const [selectedStatus, setSelectedStatus] = useState<string>('all')\n  const [showForm, setShowForm] = useState(false)\n  const [editingModel, setEditingModel] = useState<AIModel | undefined>()\n  const [deletingModel, setDeletingModel] = useState<AIModel | undefined>()\n  const [stats, setStats] = useState<any>({})\n\n  // جلب النماذج\n  const fetchModels = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/ai-models?include_inactive=true')\n      const data = await response.json()\n      \n      if (response.ok) {\n        setModels(data.models)\n        setStats(data.stats)\n      } else {\n        toast.error(data.error || 'خطأ في جلب النماذج')\n      }\n    } catch (error) {\n      console.error('Error fetching models:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // تطبيق الفلاتر\n  useEffect(() => {\n    let filtered = models\n\n    // البحث النصي\n    if (searchTerm) {\n      filtered = filtered.filter(model =>\n        model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        model.description.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // فلترة حسب المقدم\n    if (selectedProvider !== 'all') {\n      filtered = filtered.filter(model => model.provider === selectedProvider)\n    }\n\n    // فلترة حسب النوع\n    if (selectedType !== 'all') {\n      filtered = filtered.filter(model => model.type === selectedType)\n    }\n\n    // فلترة حسب الحالة\n    if (selectedStatus !== 'all') {\n      if (selectedStatus === 'active') {\n        filtered = filtered.filter(model => model.isActive)\n      } else if (selectedStatus === 'inactive') {\n        filtered = filtered.filter(model => !model.isActive)\n      } else {\n        filtered = filtered.filter(model => model.status === selectedStatus)\n      }\n    }\n\n    setFilteredModels(filtered)\n  }, [models, searchTerm, selectedProvider, selectedType, selectedStatus])\n\n  useEffect(() => {\n    fetchModels()\n  }, [])\n\n  // حفظ النموذج\n  const handleSaveModel = async (data: CreateModelRequest | UpdateModelRequest) => {\n    try {\n      const url = editingModel ? `/api/ai-models/${editingModel.id}` : '/api/ai-models'\n      const method = editingModel ? 'PUT' : 'POST'\n      \n      const response = await fetch(url, {\n        method,\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(data)\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        toast.success(result.message)\n        setShowForm(false)\n        setEditingModel(undefined)\n        fetchModels()\n      } else {\n        toast.error(result.error)\n      }\n    } catch (error) {\n      console.error('Error saving model:', error)\n      toast.error('خطأ في حفظ النموذج')\n    }\n  }\n\n  // حذف النموذج\n  const handleDeleteModel = async () => {\n    if (!deletingModel) return\n\n    try {\n      const response = await fetch(`/api/ai-models/${deletingModel.id}`, {\n        method: 'DELETE'\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        toast.success(result.message)\n        setDeletingModel(undefined)\n        fetchModels()\n      } else {\n        toast.error(result.error)\n      }\n    } catch (error) {\n      console.error('Error deleting model:', error)\n      toast.error('خطأ في حذف النموذج')\n    }\n  }\n\n  // تفعيل/إلغاء تفعيل النموذج\n  const handleToggleActive = async (model: AIModel, isActive: boolean) => {\n    try {\n      const response = await fetch(`/api/ai-models/${model.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ isActive })\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        toast.success(result.message)\n        fetchModels()\n      } else {\n        toast.error(result.error)\n      }\n    } catch (error) {\n      console.error('Error toggling model:', error)\n      toast.error('خطأ في تحديث النموذج')\n    }\n  }\n\n  // اختبار النموذج\n  const handleTestModel = async (model: AIModel) => {\n    try {\n      const response = await fetch('/api/ai-models/test', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          modelId: model.id,\n          prompt: 'مرحباً، هذا اختبار للنموذج'\n        })\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        if (result.success) {\n          toast.success(`نجح اختبار النموذج (${result.responseTime}ms)`)\n        } else {\n          toast.error(`فشل اختبار النموذج: ${result.error}`)\n        }\n        fetchModels()\n      } else {\n        toast.error('خطأ في اختبار النموذج')\n      }\n    } catch (error) {\n      console.error('Error testing model:', error)\n      toast.error('خطأ في اختبار النموذج')\n    }\n  }\n\n  const providers = Array.from(new Set(models.map(m => m.provider)))\n  const types = Array.from(new Set(models.map(m => m.type)))\n\n  return (\n    <ProtectedRoute requiredRole={UserRole.ADMIN}>\n      <div className=\"min-h-screen bg-background\">\n        <AdminDashboardHeader />\n        \n        <div className=\"container mx-auto px-4 py-8\">\n          {/* العنوان والإحصائيات */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div>\n              <h1 className=\"text-3xl font-bold arabic-text flex items-center gap-3\">\n                <Brain className=\"h-8 w-8 text-primary\" />\n                إدارة نماذج الذكاء الاصطناعي\n              </h1>\n              <p className=\"text-muted-foreground mt-2\">\n                إدارة وتكوين نماذج الذكاء الاصطناعي المختلفة\n              </p>\n            </div>\n            \n            <div className=\"flex gap-2\">\n              <Button onClick={() => fetchModels()} variant=\"outline\">\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                تحديث\n              </Button>\n              <Button onClick={() => setShowForm(true)}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                إضافة نموذج\n              </Button>\n            </div>\n          </div>\n\n          {/* بطاقات الإحصائيات */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">إجمالي النماذج</CardTitle>\n                <Brain className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.total || 0}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  {stats.active || 0} نشط، {stats.inactive || 0} غير نشط\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">مقدمو الخدمة</CardTitle>\n                <Settings className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{providers.length}</div>\n                <div className=\"flex flex-wrap gap-1 mt-2\">\n                  {providers.slice(0, 3).map(provider => (\n                    <Badge key={provider} variant=\"secondary\" className=\"text-xs\">\n                      {provider}\n                    </Badge>\n                  ))}\n                  {providers.length > 3 && (\n                    <Badge variant=\"outline\" className=\"text-xs\">\n                      +{providers.length - 3}\n                    </Badge>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">إجمالي الطلبات</CardTitle>\n                <Activity className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  {models.reduce((sum, m) => sum + m.usage.totalRequests, 0).toLocaleString()}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  عبر جميع النماذج\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">إجمالي التكلفة</CardTitle>\n                <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  ${models.reduce((sum, m) => sum + m.usage.totalCost, 0).toFixed(2)}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  هذا الشهر\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* أدوات البحث والفلترة */}\n          <Card className=\"mb-6\">\n            <CardContent className=\"pt-6\">\n              <div className=\"flex flex-col md:flex-row gap-4\">\n                <div className=\"flex-1\">\n                  <div className=\"relative\">\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                    <Input\n                      placeholder=\"البحث في النماذج...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"pl-10\"\n                    />\n                  </div>\n                </div>\n                \n                <Select value={selectedProvider} onValueChange={setSelectedProvider}>\n                  <SelectTrigger className=\"w-full md:w-48\">\n                    <SelectValue placeholder=\"مقدم الخدمة\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">جميع المقدمين</SelectItem>\n                    {providers.map(provider => (\n                      <SelectItem key={provider} value={provider}>\n                        {provider}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n\n                <Select value={selectedType} onValueChange={setSelectedType}>\n                  <SelectTrigger className=\"w-full md:w-48\">\n                    <SelectValue placeholder=\"نوع النموذج\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">جميع الأنواع</SelectItem>\n                    {types.map(type => (\n                      <SelectItem key={type} value={type}>\n                        {type}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n\n                <Select value={selectedStatus} onValueChange={setSelectedStatus}>\n                  <SelectTrigger className=\"w-full md:w-48\">\n                    <SelectValue placeholder=\"الحالة\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">جميع الحالات</SelectItem>\n                    <SelectItem value=\"active\">نشط</SelectItem>\n                    <SelectItem value=\"inactive\">غير نشط</SelectItem>\n                    <SelectItem value=\"error\">خطأ</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* قائمة النماذج */}\n          {loading ? (\n            <div className=\"text-center py-12\">\n              <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n              <p>جاري تحميل النماذج...</p>\n            </div>\n          ) : filteredModels.length === 0 ? (\n            <Card>\n              <CardContent className=\"text-center py-12\">\n                <Brain className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground\" />\n                <h3 className=\"text-lg font-semibold mb-2\">لا توجد نماذج</h3>\n                <p className=\"text-muted-foreground mb-4\">\n                  {searchTerm || selectedProvider !== 'all' || selectedType !== 'all' || selectedStatus !== 'all'\n                    ? 'لا توجد نماذج تطابق معايير البحث'\n                    : 'لم يتم إضافة أي نماذج بعد'\n                  }\n                </p>\n                <Button onClick={() => setShowForm(true)}>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  إضافة نموذج جديد\n                </Button>\n              </CardContent>\n            </Card>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {filteredModels.map((model) => (\n                <AIModelCard\n                  key={model.id}\n                  model={model}\n                  onEdit={(model) => {\n                    setEditingModel(model)\n                    setShowForm(true)\n                  }}\n                  onDelete={(model) => setDeletingModel(model)}\n                  onTest={handleTestModel}\n                  onToggleActive={handleToggleActive}\n                  onViewUsage={(model) => {\n                    // TODO: فتح صفحة الإحصائيات\n                    toast.info('سيتم إضافة صفحة الإحصائيات قريباً')\n                  }}\n                  onSettings={(model) => {\n                    setEditingModel(model)\n                    setShowForm(true)\n                  }}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* نموذج إضافة/تحرير */}\n        <AIModelForm\n          model={editingModel}\n          isOpen={showForm}\n          onClose={() => {\n            setShowForm(false)\n            setEditingModel(undefined)\n          }}\n          onSave={handleSaveModel}\n          onTest={async (data) => {\n            await handleTestModel(editingModel!)\n          }}\n        />\n\n        {/* تأكيد الحذف */}\n        <AlertDialog open={!!deletingModel} onOpenChange={() => setDeletingModel(undefined)}>\n          <AlertDialogContent>\n            <AlertDialogHeader>\n              <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>\n              <AlertDialogDescription>\n                هل أنت متأكد من حذف النموذج \"{deletingModel?.name}\"؟\n                هذا الإجراء لا يمكن التراجع عنه.\n              </AlertDialogDescription>\n            </AlertDialogHeader>\n            <AlertDialogFooter>\n              <AlertDialogCancel>إلغاء</AlertDialogCancel>\n              <AlertDialogAction onClick={handleDeleteModel} className=\"bg-destructive text-destructive-foreground\">\n                حذف\n              </AlertDialogAction>\n            </AlertDialogFooter>\n          </AlertDialogContent>\n        </AlertDialog>\n      </div>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAOA;AAUA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAhCA;;;;;;;;;;;;;;;;;AA+Ce,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAEzC,cAAc;IACd,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU,KAAK,MAAM;gBACrB,SAAS,KAAK,KAAK;YACrB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,cAAc;QACd,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,QACzB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEnE;QAEA,mBAAmB;QACnB,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;QACzD;QAEA,kBAAkB;QAClB,IAAI,iBAAiB,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;QACrD;QAEA,mBAAmB;QACnB,IAAI,mBAAmB,OAAO;YAC5B,IAAI,mBAAmB,UAAU;gBAC/B,WAAW,SAAS,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ;YACpD,OAAO,IAAI,mBAAmB,YAAY;gBACxC,WAAW,SAAS,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,QAAQ;YACrD,OAAO;gBACL,WAAW,SAAS,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;YACvD;QACF;QAEA,kBAAkB;IACpB,GAAG;QAAC;QAAQ;QAAY;QAAkB;QAAc;KAAe;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,cAAc;IACd,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,MAAM,eAAe,CAAC,eAAe,EAAE,aAAa,EAAE,EAAE,GAAG;YACjE,MAAM,SAAS,eAAe,QAAQ;YAEtC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B,YAAY;gBACZ,gBAAgB;gBAChB;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,cAAc;IACd,MAAM,oBAAoB;QACxB,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,cAAc,EAAE,EAAE,EAAE;gBACjE,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B,iBAAiB;gBACjB;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB,OAAO,OAAgB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,iBAAiB;IACjB,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,MAAM,EAAE;oBACjB,QAAQ;gBACV;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,IAAI,OAAO,OAAO,EAAE;oBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,oBAAoB,EAAE,OAAO,YAAY,CAAC,GAAG,CAAC;gBAC/D,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,oBAAoB,EAAE,OAAO,KAAK,EAAE;gBACnD;gBACA;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,YAAY,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IAC/D,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;IAEvD,qBACE,8OAAC,4IAAA,CAAA,iBAAc;QAAC,cAAc,oHAAA,CAAA,WAAQ,CAAC,KAAK;kBAC1C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,mJAAA,CAAA,uBAAoB;;;;;8BAErB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAG5C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM;4CAAe,SAAQ;;8DAC5C,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,YAAY;;8DACjC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,MAAM,KAAK,IAAI;;;;;;8DACpD,8OAAC;oDAAE,WAAU;;wDACV,MAAM,MAAM,IAAI;wDAAE;wDAAO,MAAM,QAAQ,IAAI;wDAAE;;;;;;;;;;;;;;;;;;;8CAKpD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,UAAU,MAAM;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;wDACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,yBACzB,8OAAC,iIAAA,CAAA,QAAK;gEAAgB,SAAQ;gEAAY,WAAU;0EACjD;+DADS;;;;;wDAIb,UAAU,MAAM,GAAG,mBAClB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAU;gEACzC,UAAU,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;8CAO/B,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DACZ,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,GAAG,cAAc;;;;;;8DAE3E,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;;wDAAqB;wDAChC,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC;;;;;;;8DAElE,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;sCAQnD,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;;;;;;sDAKhB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAkB,eAAe;;8DAC9C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;wDACvB,UAAU,GAAG,CAAC,CAAA,yBACb,8OAAC,kIAAA,CAAA,aAAU;gEAAgB,OAAO;0EAC/B;+DADc;;;;;;;;;;;;;;;;;sDAOvB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAc,eAAe;;8DAC1C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;wDACvB,MAAM,GAAG,CAAC,CAAA,qBACT,8OAAC,kIAAA,CAAA,aAAU;gEAAY,OAAO;0EAC3B;+DADc;;;;;;;;;;;;;;;;;sDAOvB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAgB,eAAe;;8DAC5C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQnC,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAE;;;;;;;;;;;mCAEH,eAAe,MAAM,KAAK,kBAC5B,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDACV,cAAc,qBAAqB,SAAS,iBAAiB,SAAS,mBAAmB,QACtF,qCACA;;;;;;kDAGN,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,YAAY;;0DACjC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;iDAMvC,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC,0IAAA,CAAA,cAAW;oCAEV,OAAO;oCACP,QAAQ,CAAC;wCACP,gBAAgB;wCAChB,YAAY;oCACd;oCACA,UAAU,CAAC,QAAU,iBAAiB;oCACtC,QAAQ;oCACR,gBAAgB;oCAChB,aAAa,CAAC;wCACZ,4BAA4B;wCAC5B,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oCACb;oCACA,YAAY,CAAC;wCACX,gBAAgB;wCAChB,YAAY;oCACd;mCAhBK,MAAM,EAAE;;;;;;;;;;;;;;;;8BAwBvB,8OAAC,0IAAA,CAAA,cAAW;oBACV,OAAO;oBACP,QAAQ;oBACR,SAAS;wBACP,YAAY;wBACZ,gBAAgB;oBAClB;oBACA,QAAQ;oBACR,QAAQ,OAAO;wBACb,MAAM,gBAAgB;oBACxB;;;;;;8BAIF,8OAAC,2IAAA,CAAA,cAAW;oBAAC,MAAM,CAAC,CAAC;oBAAe,cAAc,IAAM,iBAAiB;8BACvE,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;0CACjB,8OAAC,2IAAA,CAAA,oBAAiB;;kDAChB,8OAAC,2IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,8OAAC,2IAAA,CAAA,yBAAsB;;4CAAC;4CACQ,eAAe;4CAAK;;;;;;;;;;;;;0CAItD,8OAAC,2IAAA,CAAA,oBAAiB;;kDAChB,8OAAC,2IAAA,CAAA,oBAAiB;kDAAC;;;;;;kDACnB,8OAAC,2IAAA,CAAA,oBAAiB;wCAAC,SAAS;wCAAmB,WAAU;kDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpH", "debugId": null}}]}