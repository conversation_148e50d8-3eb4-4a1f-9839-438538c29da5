@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-cairo;
  }

  /* تحسين عرض النص العربي */
  html[dir="rtl"] {
    font-feature-settings: "liga" 1, "kern" 1;
  }

  /* تحسين المسافات للنص العربي */
  .arabic-text {
    line-height: 1.8;
    letter-spacing: 0.02em;
  }

  /* تحسين Grid Layout للـ RTL */
  html[dir="rtl"] .grid {
    direction: rtl;
  }

  /* تحسين Flexbox للـ RTL */
  html[dir="rtl"] .flex {
    direction: rtl;
  }

  /* تحسين عرض البطاقات والمنتجات */
  html[dir="rtl"] .product-grid,
  html[dir="rtl"] .category-grid {
    direction: rtl;
  }

  /* تحسين عرض الجداول */
  html[dir="rtl"] table {
    direction: rtl;
  }

  /* تحسين عرض الأزرار والعناصر التفاعلية */
  html[dir="rtl"] .btn-group {
    direction: rtl;
  }

  /* تحسين عرض البطاقات في الشبكة */
  html[dir="rtl"] .product-grid > *,
  html[dir="rtl"] .category-grid > * {
    direction: ltr; /* المحتوى الداخلي للبطاقات يبقى LTR */
  }

  /* تحسين عرض النصوص العربية في البطاقات */
  html[dir="rtl"] .product-grid .arabic-text,
  html[dir="rtl"] .category-grid .arabic-text {
    direction: rtl;
    text-align: right;
  }

  /* تحسين عرض الصور والأيقونات */
  html[dir="rtl"] .product-grid img,
  html[dir="rtl"] .category-grid img {
    direction: ltr;
  }

  /* تحسين عرض الأسعار والأرقام */
  html[dir="rtl"] .price,
  html[dir="rtl"] .number {
    direction: ltr;
    text-align: left;
  }

  /* تحسين عرض الجداول في RTL */
  html[dir="rtl"] table th,
  html[dir="rtl"] table td {
    text-align: right;
  }

  /* تحسين عرض الأرقام والأسعار في الجداول */
  html[dir="rtl"] table .price,
  html[dir="rtl"] table .number,
  html[dir="rtl"] table .rating {
    text-align: left;
    direction: ltr;
  }

  /* تحسين عرض الأزرار في الجداول */
  html[dir="rtl"] table .actions {
    direction: ltr;
  }
}

/* Navigation animations */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse-scale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Smooth transitions for RTL/LTR switching */
html {
  transition: direction 0.3s ease;
}

/* Enhanced focus styles for accessibility */
.focus-visible:focus-visible {
  @apply ring-2 ring-blue-500 ring-offset-2 outline-none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* تنسيق العملة RTL - العملة من الجهة اليمنى */
.currency-rtl {
  direction: ltr;
  text-align: right;
  unicode-bidi: bidi-override;
}

.currency-rtl::after {
  content: " Dhs";
  margin-left: 0.25rem;
}

/* فئة للأسعار مع تنسيق RTL */
.price-rtl {
  direction: ltr;
  text-align: right;
  display: inline-block;
}

.price-rtl .currency {
  margin-left: 0.25rem;
}

/* تحسين عرض الأرقام والعملة في البيئة العربية */
html[dir="rtl"] .price,
html[dir="rtl"] .currency,
html[dir="rtl"] .amount {
  direction: ltr;
  text-align: right;
  display: inline-block;
}

/* تنسيق خاص للجداول */
html[dir="rtl"] table .price,
html[dir="rtl"] table .currency,
html[dir="rtl"] table .amount {
  text-align: left;
}

/* تنسيق للبطاقات والكروت */
html[dir="rtl"] .card .price,
html[dir="rtl"] .card .currency,
html[dir="rtl"] .card .amount {
  text-align: right;
}
