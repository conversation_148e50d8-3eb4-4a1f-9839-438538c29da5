'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft,
  Settings,
  Globe,
  Mail,
  Shield,
  CreditCard,
  Truck,
  Bell,
  Database,
  Server,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Info,
  Upload,
  Download,
  Eye,
  EyeOff
} from 'lucide-react'
import Link from 'next/link'

// أنواع البيانات
interface SystemSettings {
  // إعدادات عامة
  site_name: string
  site_description: string
  site_logo: string
  contact_email: string
  contact_phone: string
  support_email: string
  
  // إعدادات اللغة والمنطقة
  default_language: string
  supported_languages: string[]
  default_currency: string
  timezone: string
  date_format: string
  
  // إعدادات الدفع
  payment_methods: string[]
  tax_rate: number
  shipping_cost: number
  free_shipping_threshold: number
  
  // إعدادات الإيجار
  rental_duration_days: number
  return_policy_days: number
  late_return_fee: number
  
  // إعدادات الأمان
  enable_2fa: boolean
  password_min_length: number
  session_timeout: number
  max_login_attempts: number
  
  // إعدادات الإشعارات
  email_notifications: boolean
  sms_notifications: boolean
  push_notifications: boolean
  admin_notifications: boolean
  
  // إعدادات النظام
  maintenance_mode: boolean
  allow_registration: boolean
  require_email_verification: boolean
  auto_backup: boolean
  backup_frequency: string
  
  // إعدادات التحليلات
  google_analytics_id: string
  facebook_pixel_id: string
  enable_tracking: boolean
}

// البيانات الافتراضية
const defaultSettings: SystemSettings = {
  site_name: 'منصة أزياء التخرج المغربية',
  site_description: 'منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة',
  site_logo: '/images/logo.png',
  contact_email: '<EMAIL>',
  contact_phone: '+212-5XX-XXXXXX',
  support_email: '<EMAIL>',
  
  default_language: 'ar',
  supported_languages: ['ar', 'fr', 'en'],
  default_currency: 'MAD',
  timezone: 'Africa/Casablanca',
  date_format: 'DD/MM/YYYY',
  
  payment_methods: ['credit_card', 'bank_transfer', 'cash_on_delivery'],
  tax_rate: 0.20,
  shipping_cost: 50.00,
  free_shipping_threshold: 500.00,
  
  rental_duration_days: 7,
  return_policy_days: 3,
  late_return_fee: 25.00,
  
  enable_2fa: false,
  password_min_length: 8,
  session_timeout: 30,
  max_login_attempts: 5,
  
  email_notifications: true,
  sms_notifications: false,
  push_notifications: true,
  admin_notifications: true,
  
  maintenance_mode: false,
  allow_registration: true,
  require_email_verification: true,
  auto_backup: true,
  backup_frequency: 'daily',
  
  google_analytics_id: '',
  facebook_pixel_id: '',
  enable_tracking: false
}

export default function SystemSettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings)
  const [activeTab, setActiveTab] = useState('general')
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // تحديث الإعدادات
  const updateSetting = (key: keyof SystemSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
    setHasChanges(true)
  }

  // حفظ الإعدادات
  const saveSettings = async () => {
    setIsSaving(true)
    try {
      // محاكاة حفظ الإعدادات
      await new Promise(resolve => setTimeout(resolve, 2000))
      setHasChanges(false)
      alert('تم حفظ الإعدادات بنجاح!')
    } catch (error) {
      alert('حدث خطأ أثناء حفظ الإعدادات')
    } finally {
      setIsSaving(false)
    }
  }

  // إعادة تعيين الإعدادات
  const resetSettings = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
      setSettings(defaultSettings)
      setHasChanges(true)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            href="/dashboard/admin" 
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="h-4 w-4" />
            العودة للوحة التحكم
          </Link>
          
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
                إعدادات النظام ⚙️
              </h1>
              <p className="text-gray-600 dark:text-gray-400 arabic-text">
                إدارة الإعدادات العامة وتكوين النظام
              </p>
            </div>
            
            <div className="flex gap-3 mt-4 md:mt-0">
              <Button 
                onClick={resetSettings} 
                variant="outline"
                disabled={isSaving}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                إعادة تعيين
              </Button>
              <Button 
                onClick={saveSettings} 
                disabled={!hasChanges || isSaving}
              >
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </Button>
            </div>
          </div>
        </div>

        {/* تنبيه التغييرات */}
        {hasChanges && (
          <Card className="mb-6 border-orange-200 bg-orange-50 dark:bg-orange-900/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                <span className="text-orange-800 dark:text-orange-200 arabic-text">
                  لديك تغييرات غير محفوظة. تأكد من حفظ التغييرات قبل المغادرة.
                </span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* التبويبات الرئيسية */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-6">
            <TabsTrigger value="general" className="arabic-text">
              <Globe className="h-4 w-4 mr-2" />
              عام
            </TabsTrigger>
            <TabsTrigger value="payment" className="arabic-text">
              <CreditCard className="h-4 w-4 mr-2" />
              الدفع
            </TabsTrigger>
            <TabsTrigger value="security" className="arabic-text">
              <Shield className="h-4 w-4 mr-2" />
              الأمان
            </TabsTrigger>
            <TabsTrigger value="notifications" className="arabic-text">
              <Bell className="h-4 w-4 mr-2" />
              الإشعارات
            </TabsTrigger>
            <TabsTrigger value="system" className="arabic-text">
              <Server className="h-4 w-4 mr-2" />
              النظام
            </TabsTrigger>
            <TabsTrigger value="analytics" className="arabic-text">
              <Database className="h-4 w-4 mr-2" />
              التحليلات
            </TabsTrigger>
          </TabsList>

          {/* تبويب الإعدادات العامة */}
          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">معلومات الموقع</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="site_name" className="arabic-text">اسم الموقع</Label>
                    <Input
                      id="site_name"
                      value={settings.site_name}
                      onChange={(e) => updateSetting('site_name', e.target.value)}
                      className="arabic-text"
                    />
                  </div>
                  <div>
                    <Label htmlFor="default_language" className="arabic-text">اللغة الافتراضية</Label>
                    <Select value={settings.default_language} onValueChange={(value) => updateSetting('default_language', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ar">العربية</SelectItem>
                        <SelectItem value="fr">Français</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="site_description" className="arabic-text">وصف الموقع</Label>
                  <Textarea
                    id="site_description"
                    value={settings.site_description}
                    onChange={(e) => updateSetting('site_description', e.target.value)}
                    className="arabic-text"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="contact_email" className="arabic-text">البريد الإلكتروني للتواصل</Label>
                    <Input
                      id="contact_email"
                      type="email"
                      value={settings.contact_email}
                      onChange={(e) => updateSetting('contact_email', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="contact_phone" className="arabic-text">رقم الهاتف</Label>
                    <Input
                      id="contact_phone"
                      value={settings.contact_phone}
                      onChange={(e) => updateSetting('contact_phone', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="default_currency" className="arabic-text">العملة الافتراضية</Label>
                    <Select value={settings.default_currency} onValueChange={(value) => updateSetting('default_currency', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MAD">درهم مغربي (MAD)</SelectItem>
                        <SelectItem value="EUR">يورو (EUR)</SelectItem>
                        <SelectItem value="USD">دولار أمريكي (USD)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="timezone" className="arabic-text">المنطقة الزمنية</Label>
                    <Select value={settings.timezone} onValueChange={(value) => updateSetting('timezone', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Africa/Casablanca">الدار البيضاء</SelectItem>
                        <SelectItem value="Europe/Paris">باريس</SelectItem>
                        <SelectItem value="America/New_York">نيويورك</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب إعدادات الدفع */}
          <TabsContent value="payment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إعدادات الدفع والشحن</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="tax_rate" className="arabic-text">معدل الضريبة (%)</Label>
                    <Input
                      id="tax_rate"
                      type="number"
                      step="0.01"
                      value={settings.tax_rate * 100}
                      onChange={(e) => updateSetting('tax_rate', parseFloat(e.target.value) / 100)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="shipping_cost" className="arabic-text">تكلفة الشحن (Dhs)</Label>
                    <Input
                      id="shipping_cost"
                      type="number"
                      step="0.01"
                      value={settings.shipping_cost}
                      onChange={(e) => updateSetting('shipping_cost', parseFloat(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="free_shipping_threshold" className="arabic-text">الحد الأدنى للشحن المجاني (Dhs)</Label>
                    <Input
                      id="free_shipping_threshold"
                      type="number"
                      step="0.01"
                      value={settings.free_shipping_threshold}
                      onChange={(e) => updateSetting('free_shipping_threshold', parseFloat(e.target.value))}
                    />
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="rental_duration_days" className="arabic-text">مدة الإيجار (أيام)</Label>
                    <Input
                      id="rental_duration_days"
                      type="number"
                      value={settings.rental_duration_days}
                      onChange={(e) => updateSetting('rental_duration_days', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="return_policy_days" className="arabic-text">مدة سياسة الإرجاع (أيام)</Label>
                    <Input
                      id="return_policy_days"
                      type="number"
                      value={settings.return_policy_days}
                      onChange={(e) => updateSetting('return_policy_days', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="late_return_fee" className="arabic-text">رسوم التأخير (Dhs)</Label>
                    <Input
                      id="late_return_fee"
                      type="number"
                      step="0.01"
                      value={settings.late_return_fee}
                      onChange={(e) => updateSetting('late_return_fee', parseFloat(e.target.value))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب إعدادات الأمان */}
          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إعدادات الأمان</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="arabic-text">تفعيل المصادقة الثنائية</Label>
                    <p className="text-sm text-gray-500 arabic-text">تعزيز أمان الحسابات بالمصادقة الثنائية</p>
                  </div>
                  <Switch
                    checked={settings.enable_2fa}
                    onCheckedChange={(checked) => updateSetting('enable_2fa', checked)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="password_min_length" className="arabic-text">الحد الأدنى لطول كلمة المرور</Label>
                    <Input
                      id="password_min_length"
                      type="number"
                      min="6"
                      max="20"
                      value={settings.password_min_length}
                      onChange={(e) => updateSetting('password_min_length', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="session_timeout" className="arabic-text">انتهاء الجلسة (دقيقة)</Label>
                    <Input
                      id="session_timeout"
                      type="number"
                      value={settings.session_timeout}
                      onChange={(e) => updateSetting('session_timeout', parseInt(e.target.value))}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="max_login_attempts" className="arabic-text">الحد الأقصى لمحاولات تسجيل الدخول</Label>
                  <Input
                    id="max_login_attempts"
                    type="number"
                    min="3"
                    max="10"
                    value={settings.max_login_attempts}
                    onChange={(e) => updateSetting('max_login_attempts', parseInt(e.target.value))}
                    className="w-full md:w-48"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب إعدادات الإشعارات */}
          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إعدادات الإشعارات</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">الإشعارات عبر البريد الإلكتروني</Label>
                      <p className="text-sm text-gray-500 arabic-text">إرسال إشعارات للمستخدمين عبر البريد الإلكتروني</p>
                    </div>
                    <Switch
                      checked={settings.email_notifications}
                      onCheckedChange={(checked) => updateSetting('email_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">الإشعارات عبر الرسائل النصية</Label>
                      <p className="text-sm text-gray-500 arabic-text">إرسال إشعارات عبر الرسائل النصية</p>
                    </div>
                    <Switch
                      checked={settings.sms_notifications}
                      onCheckedChange={(checked) => updateSetting('sms_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">الإشعارات الفورية</Label>
                      <p className="text-sm text-gray-500 arabic-text">إشعارات فورية في المتصفح</p>
                    </div>
                    <Switch
                      checked={settings.push_notifications}
                      onCheckedChange={(checked) => updateSetting('push_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">إشعارات الإدارة</Label>
                      <p className="text-sm text-gray-500 arabic-text">إشعارات خاصة بالمديرين</p>
                    </div>
                    <Switch
                      checked={settings.admin_notifications}
                      onCheckedChange={(checked) => updateSetting('admin_notifications', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب إعدادات النظام */}
          <TabsContent value="system" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إعدادات النظام</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">وضع الصيانة</Label>
                      <p className="text-sm text-gray-500 arabic-text">تعطيل الموقع مؤقتاً للصيانة</p>
                    </div>
                    <Switch
                      checked={settings.maintenance_mode}
                      onCheckedChange={(checked) => updateSetting('maintenance_mode', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">السماح بالتسجيل الجديد</Label>
                      <p className="text-sm text-gray-500 arabic-text">السماح للمستخدمين الجدد بإنشاء حسابات</p>
                    </div>
                    <Switch
                      checked={settings.allow_registration}
                      onCheckedChange={(checked) => updateSetting('allow_registration', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">التحقق من البريد الإلكتروني</Label>
                      <p className="text-sm text-gray-500 arabic-text">طلب التحقق من البريد الإلكتروني عند التسجيل</p>
                    </div>
                    <Switch
                      checked={settings.require_email_verification}
                      onCheckedChange={(checked) => updateSetting('require_email_verification', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="arabic-text">النسخ الاحتياطي التلقائي</Label>
                      <p className="text-sm text-gray-500 arabic-text">إنشاء نسخ احتياطية تلقائية من قاعدة البيانات</p>
                    </div>
                    <Switch
                      checked={settings.auto_backup}
                      onCheckedChange={(checked) => updateSetting('auto_backup', checked)}
                    />
                  </div>
                </div>

                {settings.auto_backup && (
                  <div>
                    <Label htmlFor="backup_frequency" className="arabic-text">تكرار النسخ الاحتياطي</Label>
                    <Select value={settings.backup_frequency} onValueChange={(value) => updateSetting('backup_frequency', value)}>
                      <SelectTrigger className="w-full md:w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hourly">كل ساعة</SelectItem>
                        <SelectItem value="daily">يومياً</SelectItem>
                        <SelectItem value="weekly">أسبوعياً</SelectItem>
                        <SelectItem value="monthly">شهرياً</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب إعدادات التحليلات */}
          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إعدادات التحليلات والتتبع</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <Label className="arabic-text">تفعيل التتبع</Label>
                    <p className="text-sm text-gray-500 arabic-text">تفعيل تتبع سلوك المستخدمين لتحسين الخدمة</p>
                  </div>
                  <Switch
                    checked={settings.enable_tracking}
                    onCheckedChange={(checked) => updateSetting('enable_tracking', checked)}
                  />
                </div>

                {settings.enable_tracking && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="google_analytics_id" className="arabic-text">معرف Google Analytics</Label>
                      <Input
                        id="google_analytics_id"
                        placeholder="GA-XXXXXXXXX-X"
                        value={settings.google_analytics_id}
                        onChange={(e) => updateSetting('google_analytics_id', e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="facebook_pixel_id" className="arabic-text">معرف Facebook Pixel</Label>
                      <Input
                        id="facebook_pixel_id"
                        placeholder="XXXXXXXXXXXXXXX"
                        value={settings.facebook_pixel_id}
                        onChange={(e) => updateSetting('facebook_pixel_id', e.target.value)}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
