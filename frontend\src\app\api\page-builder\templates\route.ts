import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'
import { TemplateCategory } from '@/types/page-builder'

// GET - جلب جميع قوالب الصفحات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') as TemplateCategory | null
    const language = searchParams.get('language') || 'ar'
    const includePremium = searchParams.get('include_premium') === 'true'
    const sortBy = searchParams.get('sort_by') || 'usageCount'
    const sortOrder = searchParams.get('sort_order') || 'desc'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    // جلب البيانات الوهمية
    let templates = MockDataManager.getPageTemplates()

    // تطبيق الفلاتر
    if (category) {
      templates = templates.filter(template => template.category === category)
    }

    if (!includePremium) {
      templates = templates.filter(template => !template.isPremium)
    }

    // الترتيب
    templates.sort((a, b) => {
      const aValue = a[sortBy as keyof typeof a] as number
      const bValue = b[sortBy as keyof typeof b] as number
      
      if (sortOrder === 'desc') {
        return bValue - aValue
      } else {
        return aValue - bValue
      }
    })

    // تطبيق التصفح
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedTemplates = templates.slice(startIndex, endIndex)

    // إحصائيات الفئات
    const categories = Array.from(new Set(MockDataManager.getPageTemplates().map(t => t.category)))
    const categoryStats = categories.map(cat => ({
      category: cat,
      count: MockDataManager.getPageTemplates().filter(t => t.category === cat).length,
      premiumCount: MockDataManager.getPageTemplates().filter(t => t.category === cat && t.isPremium).length
    }))

    return NextResponse.json({
      templates: paginatedTemplates,
      total: templates.length,
      page,
      limit,
      totalPages: Math.ceil(templates.length / limit),
      categories: categoryStats,
      stats: {
        total: MockDataManager.getPageTemplates().length,
        free: MockDataManager.getPageTemplates().filter(t => !t.isPremium).length,
        premium: MockDataManager.getPageTemplates().filter(t => t.isPremium).length,
        aiGenerated: MockDataManager.getPageTemplates().filter(t => t.isAIGenerated).length
      }
    })

  } catch (error) {
    console.error('Error fetching page templates:', error)
    return NextResponse.json(
      { error: 'خطأ في جلب قوالب الصفحات' },
      { status: 500 }
    )
  }
}

// POST - إنشاء قالب جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      nameAr,
      nameEn,
      nameFr,
      description,
      category,
      components,
      preview,
      thumbnail,
      isAIGenerated,
      isPremium,
      tags,
      metadata
    } = body

    // التحقق من البيانات المطلوبة
    if (!name || !nameAr || !category || !components) {
      return NextResponse.json(
        { error: 'الاسم والفئة والمكونات مطلوبة' },
        { status: 400 }
      )
    }

    // جلب القوالب الحالية
    const templates = MockDataManager.getPageTemplates()

    // التحقق من عدم تكرار الاسم
    const existingTemplate = templates.find(template => 
      template.name.toLowerCase() === name.toLowerCase() ||
      template.nameAr.toLowerCase() === nameAr.toLowerCase()
    )
    if (existingTemplate) {
      return NextResponse.json(
        { error: 'قالب بنفس الاسم موجود بالفعل' },
        { status: 400 }
      )
    }

    // إنشاء القالب الجديد
    const newTemplate = {
      id: MockDataManager.generateId(),
      name,
      nameAr,
      nameEn,
      nameFr,
      description,
      category,
      components: components.map((comp: any) => ({
        ...comp,
        id: comp.id || MockDataManager.generateId()
      })),
      preview: preview || '/images/templates/default-preview.jpg',
      thumbnail: thumbnail || '/images/templates/default-thumb.jpg',
      isAIGenerated: isAIGenerated || false,
      isPremium: isPremium || false,
      tags: tags || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      usageCount: 0,
      rating: 0,
      metadata: metadata || {
        colors: [],
        fonts: [],
        layout: 'single-page',
        responsive: true
      }
    }

    // حفظ القالب
    templates.push(newTemplate)
    MockDataManager.savePageTemplates(templates)

    return NextResponse.json({ 
      message: 'تم إنشاء القالب بنجاح',
      template: newTemplate 
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating page template:', error)
    return NextResponse.json(
      { error: 'خطأ في إنشاء القالب' },
      { status: 500 }
    )
  }
}

// PUT - تحديث إحصائيات الاستخدام
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { templateId, action } = body

    if (!templateId || !action) {
      return NextResponse.json(
        { error: 'معرف القالب والإجراء مطلوبان' },
        { status: 400 }
      )
    }

    const templates = MockDataManager.getPageTemplates()
    const templateIndex = templates.findIndex(t => t.id === templateId)

    if (templateIndex === -1) {
      return NextResponse.json(
        { error: 'القالب غير موجود' },
        { status: 404 }
      )
    }

    const template = templates[templateIndex]

    switch (action) {
      case 'increment_usage':
        template.usageCount += 1
        break
      case 'rate':
        const { rating } = body
        if (rating >= 1 && rating <= 5) {
          // حساب متوسط التقييم (مبسط)
          template.rating = rating
        }
        break
      default:
        return NextResponse.json(
          { error: 'إجراء غير مدعوم' },
          { status: 400 }
        )
    }

    template.updatedAt = new Date().toISOString()
    templates[templateIndex] = template
    MockDataManager.savePageTemplates(templates)

    return NextResponse.json({
      message: 'تم تحديث القالب بنجاح',
      template
    })

  } catch (error) {
    console.error('Error updating template:', error)
    return NextResponse.json(
      { error: 'خطأ في تحديث القالب' },
      { status: 500 }
    )
  }
}

// DELETE - حذف قالب
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const templateId = searchParams.get('id')

    if (!templateId) {
      return NextResponse.json(
        { error: 'معرف القالب مطلوب' },
        { status: 400 }
      )
    }

    const templates = MockDataManager.getPageTemplates()
    const templateIndex = templates.findIndex(t => t.id === templateId)

    if (templateIndex === -1) {
      return NextResponse.json(
        { error: 'القالب غير موجود' },
        { status: 404 }
      )
    }

    const template = templates[templateIndex]

    // التحقق من استخدام القالب في مشاريع موجودة
    const projects = MockDataManager.getPageProjects()
    const usedInProjects = projects.filter(p => p.templateId === templateId)

    if (usedInProjects.length > 0) {
      return NextResponse.json(
        { 
          error: 'لا يمكن حذف القالب لأنه مستخدم في مشاريع موجودة',
          usedInProjects: usedInProjects.map(p => ({ id: p.id, name: p.name }))
        },
        { status: 400 }
      )
    }

    // حذف القالب
    templates.splice(templateIndex, 1)
    MockDataManager.savePageTemplates(templates)

    return NextResponse.json({
      message: 'تم حذف القالب بنجاح',
      deletedTemplate: {
        id: template.id,
        name: template.name,
        nameAr: template.nameAr
      }
    })

  } catch (error) {
    console.error('Error deleting template:', error)
    return NextResponse.json(
      { error: 'خطأ في حذف القالب' },
      { status: 500 }
    )
  }
}
