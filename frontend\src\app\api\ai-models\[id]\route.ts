import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'
import { UpdateModelRequest } from '@/types/ai-models'

// GET - جلب نموذج واحد مع تفاصيله
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const includeUsage = searchParams.get('include_usage') === 'true'
    const includeActivities = searchParams.get('include_activities') === 'true'

    // جلب النموذج
    const models = MockDataManager.getAIModels()
    const model = models.find(m => m.id === params.id)

    if (!model) {
      return NextResponse.json(
        { error: 'النموذج غير موجود' },
        { status: 404 }
      )
    }

    // إعداد الاستجابة
    const response: any = { model }

    // إضافة بيانات الاستخدام إذا طُلبت
    if (includeUsage) {
      response.usage = model.usage
      
      // إحصائيات إضافية
      response.usageStats = {
        dailyAverage: model.usage.dailyUsage.length > 0 
          ? model.usage.dailyUsage.reduce((sum, day) => sum + day.requests, 0) / model.usage.dailyUsage.length
          : 0,
        monthlyTotal: model.usage.monthlyUsage.reduce((sum, month) => sum + month.requests, 0),
        costPerRequest: model.usage.totalRequests > 0 
          ? model.usage.totalCost / model.usage.totalRequests 
          : 0,
        tokensPerRequest: model.usage.totalRequests > 0 
          ? model.usage.totalTokens / model.usage.totalRequests 
          : 0
      }
    }

    // إضافة الأنشطة إذا طُلبت
    if (includeActivities) {
      const activities = MockDataManager.getModelActivities()
      response.activities = activities
        .filter(activity => activity.modelId === params.id)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 50) // آخر 50 نشاط
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching AI model:', error)
    return NextResponse.json(
      { error: 'خطأ في جلب النموذج' },
      { status: 500 }
    )
  }
}

// PUT - تحديث نموذج
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body: UpdateModelRequest = await request.json()
    const {
      name,
      description,
      apiKey,
      apiEndpoint,
      isActive,
      settings
    } = body

    // جلب النماذج الحالية
    const models = MockDataManager.getAIModels()
    const modelIndex = models.findIndex(m => m.id === params.id)

    if (modelIndex === -1) {
      return NextResponse.json(
        { error: 'النموذج غير موجود' },
        { status: 404 }
      )
    }

    const model = models[modelIndex]

    // التحقق من عدم تكرار الاسم (إذا تم تغييره)
    if (name && name !== model.name) {
      const existingModel = models.find(m => 
        m.name.toLowerCase() === name.toLowerCase() && 
        m.provider === model.provider &&
        m.id !== params.id
      )
      if (existingModel) {
        return NextResponse.json(
          { error: 'نموذج بنفس الاسم ومقدم الخدمة موجود بالفعل' },
          { status: 400 }
        )
      }
    }

    // تحديث البيانات
    const updatedModel = {
      ...model,
      ...(name && { name }),
      ...(description !== undefined && { description }),
      ...(apiKey !== undefined && { apiKey }),
      ...(apiEndpoint !== undefined && { apiEndpoint }),
      ...(isActive !== undefined && { isActive }),
      ...(settings && { settings: { ...model.settings, ...settings } }),
      updatedAt: new Date().toISOString()
    }

    // تحديث الحالة بناءً على التفعيل
    if (isActive !== undefined) {
      updatedModel.status = isActive ? 'active' : 'inactive'
    }

    // حفظ التحديث
    models[modelIndex] = updatedModel
    MockDataManager.saveAIModels(models)

    // إضافة نشاط
    const activities = MockDataManager.getModelActivities()
    const changes = []
    if (name && name !== model.name) changes.push(`الاسم: ${name}`)
    if (description !== undefined && description !== model.description) changes.push('الوصف')
    if (apiKey !== undefined) changes.push('مفتاح API')
    if (apiEndpoint !== undefined) changes.push('نقطة النهاية')
    if (isActive !== undefined && isActive !== model.isActive) {
      changes.push(isActive ? 'تفعيل' : 'إلغاء تفعيل')
    }
    if (settings) changes.push('الإعدادات')

    activities.push({
      id: MockDataManager.generateId(),
      modelId: params.id,
      type: 'config_change',
      description: `تم تحديث النموذج: ${changes.join(', ')}`,
      timestamp: new Date().toISOString(),
      success: true
    })
    MockDataManager.saveModelActivities(activities)

    return NextResponse.json({ 
      message: 'تم تحديث النموذج بنجاح',
      model: updatedModel 
    })

  } catch (error) {
    console.error('Error updating AI model:', error)
    return NextResponse.json(
      { error: 'خطأ في تحديث النموذج' },
      { status: 500 }
    )
  }
}

// DELETE - حذف نموذج
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // جلب النماذج الحالية
    const models = MockDataManager.getAIModels()
    const modelIndex = models.findIndex(m => m.id === params.id)

    if (modelIndex === -1) {
      return NextResponse.json(
        { error: 'النموذج غير موجود' },
        { status: 404 }
      )
    }

    const model = models[modelIndex]

    // التحقق من وجود استخدام حديث
    const hasRecentUsage = model.usage.totalRequests > 0 && 
      model.usage.lastUsed && 
      new Date(model.usage.lastUsed).getTime() > Date.now() - (7 * 24 * 60 * 60 * 1000) // آخر 7 أيام

    if (hasRecentUsage) {
      return NextResponse.json(
        { 
          error: 'لا يمكن حذف النموذج لوجود استخدام حديث. يرجى إلغاء تفعيله بدلاً من ذلك.',
          suggestion: 'deactivate'
        },
        { status: 400 }
      )
    }

    // حذف النموذج
    models.splice(modelIndex, 1)
    MockDataManager.saveAIModels(models)

    // إضافة نشاط الحذف
    const activities = MockDataManager.getModelActivities()
    activities.push({
      id: MockDataManager.generateId(),
      modelId: params.id,
      type: 'config_change',
      description: `تم حذف النموذج: ${model.name}`,
      timestamp: new Date().toISOString(),
      success: true
    })
    MockDataManager.saveModelActivities(activities)

    return NextResponse.json({ 
      message: 'تم حذف النموذج بنجاح',
      deletedModel: {
        id: model.id,
        name: model.name,
        provider: model.provider
      }
    })

  } catch (error) {
    console.error('Error deleting AI model:', error)
    return NextResponse.json(
      { error: 'خطأ في حذف النموذج' },
      { status: 500 }
    )
  }
}

// PATCH - إجراءات خاصة على النموذج
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { action, data } = body

    if (!action) {
      return NextResponse.json(
        { error: 'الإجراء مطلوب' },
        { status: 400 }
      )
    }

    const models = MockDataManager.getAIModels()
    const modelIndex = models.findIndex(m => m.id === params.id)

    if (modelIndex === -1) {
      return NextResponse.json(
        { error: 'النموذج غير موجود' },
        { status: 404 }
      )
    }

    const model = models[modelIndex]
    const activities = MockDataManager.getModelActivities()

    switch (action) {
      case 'test_connection':
        // محاكاة اختبار الاتصال
        const testSuccess = Math.random() > 0.1 // 90% نجاح
        const responseTime = Math.floor(Math.random() * 3000) + 500 // 500-3500ms
        
        model.lastTestedAt = new Date().toISOString()
        model.testResult = {
          success: testSuccess,
          responseTime,
          error: testSuccess ? undefined : 'فشل في الاتصال بالخدمة'
        }
        
        if (testSuccess) {
          model.status = 'active'
        } else {
          model.status = 'error'
        }

        activities.push({
          id: MockDataManager.generateId(),
          modelId: params.id,
          type: 'test',
          description: `اختبار الاتصال: ${testSuccess ? 'نجح' : 'فشل'}`,
          timestamp: new Date().toISOString(),
          duration: responseTime,
          success: testSuccess,
          errorMessage: testSuccess ? undefined : 'فشل في الاتصال'
        })
        break

      case 'reset_usage':
        // إعادة تعيين إحصائيات الاستخدام
        model.usage = {
          totalRequests: 0,
          totalTokens: 0,
          totalCost: 0,
          dailyUsage: [],
          monthlyUsage: [],
          averageResponseTime: 0,
          successRate: 0
        }

        activities.push({
          id: MockDataManager.generateId(),
          modelId: params.id,
          type: 'config_change',
          description: 'تم إعادة تعيين إحصائيات الاستخدام',
          timestamp: new Date().toISOString(),
          success: true
        })
        break

      case 'add_submodel':
        // إضافة نموذج فرعي
        if (!data || !data.name) {
          return NextResponse.json(
            { error: 'بيانات النموذج الفرعي مطلوبة' },
            { status: 400 }
          )
        }

        const newSubModel = {
          id: MockDataManager.generateId(),
          name: data.name,
          modelId: params.id,
          description: data.description || '',
          version: data.version || '1.0',
          capabilities: data.capabilities || [],
          pricing: data.pricing || { inputTokens: 0, outputTokens: 0, currency: 'USD', unit: '1K tokens' },
          limits: data.limits || { maxTokens: 4096, requestsPerMinute: 60, requestsPerDay: 1000, contextWindow: 4096 },
          isActive: true,
          isDefault: false,
          tags: data.tags || [],
          releaseDate: new Date().toISOString()
        }

        model.subModels.push(newSubModel)

        activities.push({
          id: MockDataManager.generateId(),
          modelId: params.id,
          type: 'config_change',
          description: `تم إضافة نموذج فرعي: ${data.name}`,
          timestamp: new Date().toISOString(),
          success: true
        })
        break

      default:
        return NextResponse.json(
          { error: 'إجراء غير مدعوم' },
          { status: 400 }
        )
    }

    // حفظ التغييرات
    model.updatedAt = new Date().toISOString()
    models[modelIndex] = model
    MockDataManager.saveAIModels(models)
    MockDataManager.saveModelActivities(activities)

    return NextResponse.json({
      message: 'تم تنفيذ الإجراء بنجاح',
      model,
      action
    })

  } catch (error) {
    console.error('Error executing model action:', error)
    return NextResponse.json(
      { error: 'خطأ في تنفيذ الإجراء' },
      { status: 500 }
    )
  }
}
