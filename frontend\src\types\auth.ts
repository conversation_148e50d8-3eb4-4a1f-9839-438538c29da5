// أنواع البيانات للمصادقة والتفويض

export enum UserRole {
  STUDENT = 'student',
  SCHOOL = 'school',
  ADMIN = 'admin',
  DELIVERY = 'delivery'
}

export interface UserProfile {
  id: string
  email: string
  full_name: string
  role: UserRole
  phone?: string
  school_name?: string
  created_at: string
  updated_at: string
}

// نوع مبسط للمستخدم للتطوير
export interface User {
  id: string
  email?: string
}

export interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  signUp: (email: string, password: string, userData: {
    full_name: string
    role: UserRole
    phone?: string
    school_name?: string
  }) => Promise<{ data: unknown, error: string | null }>
  signIn: (email: string, password: string) => Promise<{ data: unknown, error: string | null }>
  signOut: () => Promise<{ error: string | null }>
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ data: unknown, error: string | null }>
  hasRole: (requiredRole: UserRole) => boolean
}

// أنواع إضافية للمصادقة
export interface SignUpData {
  email: string
  password: string
  full_name: string
  role: UserRole
  phone?: string
  school_name?: string
}

export interface SignInData {
  email: string
  password: string
}

export interface AuthResponse {
  data: unknown
  error: string | null
}

export interface SessionData {
  user: User
  profile: UserProfile
  timestamp: number
}

// أذونات الأدوار
export const RolePermissions = {
  [UserRole.ADMIN]: [
    'manage_users',
    'manage_products',
    'manage_orders',
    'manage_schools',
    'manage_categories',
    'manage_pages',
    'manage_menu',
    'manage_ai_models',
    'manage_page_builder',
    'view_analytics',
    'manage_settings'
  ],
  [UserRole.SCHOOL]: [
    'view_students',
    'manage_school_orders',
    'view_school_analytics',
    'manage_school_profile'
  ],
  [UserRole.DELIVERY]: [
    'view_assigned_orders',
    'update_delivery_status',
    'view_delivery_routes'
  ],
  [UserRole.STUDENT]: [
    'place_orders',
    'view_own_orders',
    'update_profile'
  ]
} as const

export type Permission = typeof RolePermissions[UserRole][number]

// دالة مساعدة للتحقق من الأذونات
export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  return RolePermissions[userRole].includes(permission as any)
}

// دالة مساعدة للتحقق من الدور الهرمي
export function hasRoleOrHigher(userRole: UserRole, requiredRole: UserRole): boolean {
  const roleHierarchy = {
    [UserRole.ADMIN]: 4,
    [UserRole.SCHOOL]: 3,
    [UserRole.DELIVERY]: 2,
    [UserRole.STUDENT]: 1
  }

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole]
}
