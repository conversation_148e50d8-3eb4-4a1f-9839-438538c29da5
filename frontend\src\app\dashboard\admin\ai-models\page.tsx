'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole } from '@/types/auth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AdminDashboardHeader } from '@/components/admin/AdminDashboardHeader'
import { AIModelCard } from '@/components/admin/AIModelCard'
import { AIModelForm } from '@/components/admin/AIModelForm'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { 
  Brain, 
  Plus, 
  Search, 
  Filter, 
  BarChart3, 
  Activity, 
  DollarSign, 
  Zap,
  RefreshCw,
  Settings,
  TestTube
} from 'lucide-react'
import { AIModel, CreateModelRequest, UpdateModelRequest } from '@/types/ai-models'

export default function AIModelsPage() {
  const { user, profile } = useAuth()
  const [models, setModels] = useState<AIModel[]>([])
  const [filteredModels, setFilteredModels] = useState<AIModel[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProvider, setSelectedProvider] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [showForm, setShowForm] = useState(false)
  const [editingModel, setEditingModel] = useState<AIModel | undefined>()
  const [deletingModel, setDeletingModel] = useState<AIModel | undefined>()
  const [stats, setStats] = useState<any>({})

  // جلب النماذج
  const fetchModels = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/ai-models?include_inactive=true')
      const data = await response.json()
      
      if (response.ok) {
        setModels(data.models)
        setStats(data.stats)
      } else {
        toast.error(data.error || 'خطأ في جلب النماذج')
      }
    } catch (error) {
      console.error('Error fetching models:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  // تطبيق الفلاتر
  useEffect(() => {
    let filtered = models

    // البحث النصي
    if (searchTerm) {
      filtered = filtered.filter(model =>
        model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        model.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // فلترة حسب المقدم
    if (selectedProvider !== 'all') {
      filtered = filtered.filter(model => model.provider === selectedProvider)
    }

    // فلترة حسب النوع
    if (selectedType !== 'all') {
      filtered = filtered.filter(model => model.type === selectedType)
    }

    // فلترة حسب الحالة
    if (selectedStatus !== 'all') {
      if (selectedStatus === 'active') {
        filtered = filtered.filter(model => model.isActive)
      } else if (selectedStatus === 'inactive') {
        filtered = filtered.filter(model => !model.isActive)
      } else {
        filtered = filtered.filter(model => model.status === selectedStatus)
      }
    }

    setFilteredModels(filtered)
  }, [models, searchTerm, selectedProvider, selectedType, selectedStatus])

  useEffect(() => {
    fetchModels()
  }, [])

  // حفظ النموذج
  const handleSaveModel = async (data: CreateModelRequest | UpdateModelRequest) => {
    try {
      const url = editingModel ? `/api/ai-models/${editingModel.id}` : '/api/ai-models'
      const method = editingModel ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      
      const result = await response.json()
      
      if (response.ok) {
        toast.success(result.message)
        setShowForm(false)
        setEditingModel(undefined)
        fetchModels()
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      console.error('Error saving model:', error)
      toast.error('خطأ في حفظ النموذج')
    }
  }

  // حذف النموذج
  const handleDeleteModel = async () => {
    if (!deletingModel) return

    try {
      const response = await fetch(`/api/ai-models/${deletingModel.id}`, {
        method: 'DELETE'
      })
      
      const result = await response.json()
      
      if (response.ok) {
        toast.success(result.message)
        setDeletingModel(undefined)
        fetchModels()
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      console.error('Error deleting model:', error)
      toast.error('خطأ في حذف النموذج')
    }
  }

  // تفعيل/إلغاء تفعيل النموذج
  const handleToggleActive = async (model: AIModel, isActive: boolean) => {
    try {
      const response = await fetch(`/api/ai-models/${model.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive })
      })
      
      const result = await response.json()
      
      if (response.ok) {
        toast.success(result.message)
        fetchModels()
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      console.error('Error toggling model:', error)
      toast.error('خطأ في تحديث النموذج')
    }
  }

  // اختبار النموذج
  const handleTestModel = async (model: AIModel) => {
    try {
      const response = await fetch('/api/ai-models/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          modelId: model.id,
          prompt: 'مرحباً، هذا اختبار للنموذج'
        })
      })
      
      const result = await response.json()
      
      if (response.ok) {
        if (result.success) {
          toast.success(`نجح اختبار النموذج (${result.responseTime}ms)`)
        } else {
          toast.error(`فشل اختبار النموذج: ${result.error}`)
        }
        fetchModels()
      } else {
        toast.error('خطأ في اختبار النموذج')
      }
    } catch (error) {
      console.error('Error testing model:', error)
      toast.error('خطأ في اختبار النموذج')
    }
  }

  const providers = Array.from(new Set(models.map(m => m.provider)))
  const types = Array.from(new Set(models.map(m => m.type)))

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <div className="min-h-screen bg-background">
        <AdminDashboardHeader />
        
        <div className="container mx-auto px-4 py-8">
          {/* العنوان والإحصائيات */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold arabic-text flex items-center gap-3">
                <Brain className="h-8 w-8 text-primary" />
                إدارة نماذج الذكاء الاصطناعي
              </h1>
              <p className="text-muted-foreground mt-2">
                إدارة وتكوين نماذج الذكاء الاصطناعي المختلفة
              </p>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={() => fetchModels()} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                تحديث
              </Button>
              <Button onClick={() => setShowForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                إضافة نموذج
              </Button>
            </div>
          </div>

          {/* بطاقات الإحصائيات */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي النماذج</CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.active || 0} نشط، {stats.inactive || 0} غير نشط
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">مقدمو الخدمة</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{providers.length}</div>
                <div className="flex flex-wrap gap-1 mt-2">
                  {providers.slice(0, 3).map(provider => (
                    <Badge key={provider} variant="secondary" className="text-xs">
                      {provider}
                    </Badge>
                  ))}
                  {providers.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{providers.length - 3}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي الطلبات</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {models.reduce((sum, m) => sum + m.usage.totalRequests, 0).toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  عبر جميع النماذج
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي التكلفة</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${models.reduce((sum, m) => sum + m.usage.totalCost, 0).toFixed(2)}
                </div>
                <p className="text-xs text-muted-foreground">
                  هذا الشهر
                </p>
              </CardContent>
            </Card>
          </div>

          {/* أدوات البحث والفلترة */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="البحث في النماذج..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="مقدم الخدمة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع المقدمين</SelectItem>
                    {providers.map(provider => (
                      <SelectItem key={provider} value={provider}>
                        {provider}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="نوع النموذج" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    {types.map(type => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="active">نشط</SelectItem>
                    <SelectItem value="inactive">غير نشط</SelectItem>
                    <SelectItem value="error">خطأ</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* قائمة النماذج */}
          {loading ? (
            <div className="text-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>جاري تحميل النماذج...</p>
            </div>
          ) : filteredModels.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Brain className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">لا توجد نماذج</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || selectedProvider !== 'all' || selectedType !== 'all' || selectedStatus !== 'all'
                    ? 'لا توجد نماذج تطابق معايير البحث'
                    : 'لم يتم إضافة أي نماذج بعد'
                  }
                </p>
                <Button onClick={() => setShowForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة نموذج جديد
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredModels.map((model) => (
                <AIModelCard
                  key={model.id}
                  model={model}
                  onEdit={(model) => {
                    setEditingModel(model)
                    setShowForm(true)
                  }}
                  onDelete={(model) => setDeletingModel(model)}
                  onTest={handleTestModel}
                  onToggleActive={handleToggleActive}
                  onViewUsage={(model) => {
                    // TODO: فتح صفحة الإحصائيات
                    toast.info('سيتم إضافة صفحة الإحصائيات قريباً')
                  }}
                  onSettings={(model) => {
                    setEditingModel(model)
                    setShowForm(true)
                  }}
                />
              ))}
            </div>
          )}
        </div>

        {/* نموذج إضافة/تحرير */}
        <AIModelForm
          model={editingModel}
          isOpen={showForm}
          onClose={() => {
            setShowForm(false)
            setEditingModel(undefined)
          }}
          onSave={handleSaveModel}
          onTest={async (data) => {
            await handleTestModel(editingModel!)
          }}
        />

        {/* تأكيد الحذف */}
        <AlertDialog open={!!deletingModel} onOpenChange={() => setDeletingModel(undefined)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
              <AlertDialogDescription>
                هل أنت متأكد من حذف النموذج "{deletingModel?.name}"؟
                هذا الإجراء لا يمكن التراجع عنه.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>إلغاء</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteModel} className="bg-destructive text-destructive-foreground">
                حذف
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </ProtectedRoute>
  )
}
