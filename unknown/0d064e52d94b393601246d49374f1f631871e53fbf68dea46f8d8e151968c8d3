{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Loader2 } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: UserRole\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({\n  children,\n  requiredRole,\n  redirectTo = '/auth'\n}: ProtectedRouteProps) {\n  const { user, profile, loading, hasRole } = useAuth()\n  const router = useRouter()\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  useEffect(() => {\n    if (mounted && !loading) {\n      if (!user) {\n        router.push(redirectTo)\n        return\n      }\n\n      if (requiredRole && !hasRole(requiredRole)) {\n        router.push('/unauthorized')\n        return\n      }\n    }\n  }, [mounted, user, profile, loading, requiredRole, hasRole, router, redirectTo])\n\n  // Show loading until mounted and auth is resolved\n  if (!mounted || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\n            <Loader2 className=\"h-8 w-8 animate-spin text-blue-600 mb-4\" />\n            <p className=\"text-gray-600 dark:text-gray-300\">جاري التحميل...</p>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect\n  }\n\n  if (requiredRole && !hasRole(requiredRole)) {\n    return null // Will redirect\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AAeO,SAAS,eAAe,EAC7B,QAAQ,EACR,YAAY,EACZ,aAAa,OAAO,EACA;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,CAAC,SAAS;YACvB,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;gBAC1C,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF,GAAG;QAAC;QAAS;QAAM;QAAS;QAAS;QAAc;QAAS;QAAQ;KAAW;IAE/E,kDAAkD;IAClD,IAAI,CAAC,WAAW,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;QAC1C,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AdminQuickNav.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport {\n  Plus,\n  Menu,\n  Package,\n  Download,\n  FileText,\n  Users,\n  Settings\n} from 'lucide-react'\n\ninterface QuickNavItem {\n  label: string\n  href?: string\n  icon: React.ReactNode\n  variant?: 'default' | 'outline' | 'secondary'\n  disabled?: boolean\n}\n\nconst quickNavItems: QuickNavItem[] = [\n  {\n    label: 'إضافة صفحة',\n    href: '/dashboard/admin/pages-management',\n    icon: <Plus className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline'\n  },\n  {\n    label: 'تحرير القائمة',\n    href: '/dashboard/admin/menu-management',\n    icon: <Menu className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline'\n  },\n  {\n    label: 'إدارة المنتجات',\n    href: '/dashboard/admin/products',\n    icon: <Package className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline'\n  },\n  {\n    label: 'تصدير التقارير',\n    icon: <Download className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline',\n    disabled: true\n  }\n]\n\nexport function AdminQuickNav() {\n  return (\n    <div className=\"flex gap-3 flex-wrap\">\n      {quickNavItems.map((item, index) => {\n        const ButtonComponent = (\n          <Button \n            key={index}\n            variant={item.variant || 'outline'} \n            size=\"sm\"\n            disabled={item.disabled}\n            className=\"arabic-text\"\n          >\n            {item.icon}\n            {item.label}\n          </Button>\n        )\n\n        return item.href && !item.disabled ? (\n          <Link key={index} href={item.href}>\n            {ButtonComponent}\n          </Link>\n        ) : (\n          ButtonComponent\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAsBA,MAAM,gBAAgC;IACpC;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,SAAS;IACX;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,SAAS;IACX;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,SAAS;IACX;IACA;QACE,OAAO;QACP,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,SAAS;QACT,UAAU;IACZ;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC,MAAM;YACxB,MAAM,gCACJ,8OAAC,kIAAA,CAAA,SAAM;gBAEL,SAAS,KAAK,OAAO,IAAI;gBACzB,MAAK;gBACL,UAAU,KAAK,QAAQ;gBACvB,WAAU;;oBAET,KAAK,IAAI;oBACT,KAAK,KAAK;;eAPN;;;;;YAWT,OAAO,KAAK,IAAI,IAAI,CAAC,KAAK,QAAQ,iBAChC,8OAAC,4JAAA,CAAA,UAAI;gBAAa,MAAM,KAAK,IAAI;0BAC9B;eADQ;;;;uBAIX;QAEJ;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AdminDashboardHeader.tsx"], "sourcesContent": ["\"use client\"\n\nimport { AdminQuickNav } from './AdminQuickNav'\nimport { Badge } from '@/components/ui/badge'\nimport { Bell, Crown } from 'lucide-react'\n\ninterface AdminDashboardHeaderProps {\n  alertsCount?: number\n}\n\nexport function AdminDashboardHeader({ alertsCount = 0 }: AdminDashboardHeaderProps) {\n  return (\n    <div className=\"mb-8\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text flex items-center gap-2\">\n            <Crown className=\"h-8 w-8 text-yellow-500\" />\n            لوحة تحكم الإدارة\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n            إدارة شاملة للمنصة والمستخدمين والطلبات\n          </p>\n          {alertsCount > 0 && (\n            <div className=\"flex items-center gap-2 mt-2\">\n              <Bell className=\"h-4 w-4 text-amber-500\" />\n              <Badge variant=\"destructive\" className=\"text-xs\">\n                {alertsCount} تنبيه جديد\n              </Badge>\n            </div>\n          )}\n        </div>\n        \n        <AdminQuickNav />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAUO,SAAS,qBAAqB,EAAE,cAAc,CAAC,EAA6B;IACjF,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;sCAG/C,8OAAC;4BAAE,WAAU;sCAAoD;;;;;;wBAGhE,cAAc,mBACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;;wCACpC;wCAAY;;;;;;;;;;;;;;;;;;;8BAMrB,8OAAC,4IAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/PageBuilder.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { PageProject, PageTemplate, AIGenerationRequest } from '@/types/page-builder'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { toast } from 'sonner'\nimport { \n  Wand2, \n  Layout, \n  Palette, \n  Eye, \n  Save, \n  Download, \n  Upload,\n  Plus,\n  Grid,\n  Smartphone,\n  Tablet,\n  Monitor,\n  Zap,\n  Sparkles,\n  RefreshCw\n} from 'lucide-react'\n\ninterface PageBuilderProps {\n  project?: PageProject\n  onSave?: (project: PageProject) => void\n  onPreview?: (project: PageProject) => void\n  onPublish?: (project: PageProject) => void\n}\n\nexport function PageBuilder({ project, onSave, onPreview, onPublish }: PageBuilderProps) {\n  const [currentProject, setCurrentProject] = useState<PageProject | null>(project || null)\n  const [templates, setTemplates] = useState<PageTemplate[]>([])\n  const [showAIDialog, setShowAIDialog] = useState(false)\n  const [showTemplateDialog, setShowTemplateDialog] = useState(false)\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')\n  const [aiPrompt, setAiPrompt] = useState('')\n  const [selectedTemplate, setSelectedTemplate] = useState<PageTemplate | null>(null)\n\n  // جلب القوالب\n  const fetchTemplates = async () => {\n    try {\n      const response = await fetch('/api/page-builder/templates')\n      const data = await response.json()\n      \n      if (response.ok) {\n        setTemplates(data.templates)\n      } else {\n        toast.error(data.error || 'خطأ في جلب القوالب')\n      }\n    } catch (error) {\n      console.error('Error fetching templates:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n    }\n  }\n\n  useEffect(() => {\n    fetchTemplates()\n  }, [])\n\n  // إنشاء مشروع جديد\n  const createNewProject = async (name: string, templateId?: string) => {\n    try {\n      const response = await fetch('/api/page-builder', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          name,\n          templateId,\n          generationMode: templateId ? 'template' : 'manual'\n        })\n      })\n      \n      const data = await response.json()\n      \n      if (response.ok) {\n        setCurrentProject(data.project)\n        toast.success(data.message)\n      } else {\n        toast.error(data.error)\n      }\n    } catch (error) {\n      console.error('Error creating project:', error)\n      toast.error('خطأ في إنشاء المشروع')\n    }\n  }\n\n  // توليد صفحة بالذكاء الاصطناعي\n  const generateWithAI = async () => {\n    if (!aiPrompt.trim()) {\n      toast.error('يرجى إدخال وصف للصفحة')\n      return\n    }\n\n    setIsGenerating(true)\n    try {\n      const generationRequest: AIGenerationRequest = {\n        prompt: aiPrompt,\n        language: 'ar',\n        includeImages: true,\n        includeText: true\n      }\n\n      const response = await fetch('/api/page-builder/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(generationRequest)\n      })\n      \n      const data = await response.json()\n      \n      if (response.ok && data.success) {\n        // إنشاء مشروع جديد مع المكونات المولدة\n        const projectName = `مشروع مولد بالذكاء الاصطناعي - ${new Date().toLocaleDateString('ar-MA')}`\n        \n        const newProject: PageProject = {\n          id: Date.now().toString(),\n          name: projectName,\n          description: `مولد من: ${aiPrompt}`,\n          components: data.components || [],\n          generationMode: 'ai',\n          settings: {\n            title: projectName,\n            description: aiPrompt,\n            keywords: [],\n            language: 'ar',\n            direction: 'rtl'\n          },\n          isPublished: false,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n          createdBy: 'admin-1',\n          version: 1\n        }\n\n        setCurrentProject(newProject)\n        setShowAIDialog(false)\n        setAiPrompt('')\n        \n        toast.success('تم توليد الصفحة بنجاح!')\n        \n        if (data.suggestions && data.suggestions.length > 0) {\n          toast.info(`اقتراحات للتحسين: ${data.suggestions[0]}`)\n        }\n      } else {\n        toast.error(data.error || 'فشل في توليد الصفحة')\n      }\n    } catch (error) {\n      console.error('Error generating page:', error)\n      toast.error('خطأ في توليد الصفحة')\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  // استخدام قالب\n  const useTemplate = async (template: PageTemplate) => {\n    try {\n      const projectName = `مشروع من قالب: ${template.nameAr}`\n      \n      const newProject: PageProject = {\n        id: Date.now().toString(),\n        name: projectName,\n        description: `مبني على قالب: ${template.nameAr}`,\n        components: template.components,\n        templateId: template.id,\n        generationMode: 'template',\n        settings: {\n          title: projectName,\n          description: template.description,\n          keywords: template.tags,\n          language: 'ar',\n          direction: 'rtl'\n        },\n        isPublished: false,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        createdBy: 'admin-1',\n        version: 1\n      }\n\n      setCurrentProject(newProject)\n      setShowTemplateDialog(false)\n      \n      // تحديث إحصائيات استخدام القالب\n      await fetch('/api/page-builder/templates', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          templateId: template.id,\n          action: 'increment_usage'\n        })\n      })\n      \n      toast.success('تم تطبيق القالب بنجاح!')\n    } catch (error) {\n      console.error('Error using template:', error)\n      toast.error('خطأ في تطبيق القالب')\n    }\n  }\n\n  // حفظ المشروع\n  const saveProject = async () => {\n    if (!currentProject) return\n\n    try {\n      await onSave?.(currentProject)\n      toast.success('تم حفظ المشروع بنجاح')\n    } catch (error) {\n      console.error('Error saving project:', error)\n      toast.error('خطأ في حفظ المشروع')\n    }\n  }\n\n  // معاينة المشروع\n  const previewProject = () => {\n    if (!currentProject) return\n    onPreview?.(currentProject)\n  }\n\n  // نشر المشروع\n  const publishProject = async () => {\n    if (!currentProject) return\n\n    try {\n      await onPublish?.(currentProject)\n      toast.success('تم نشر المشروع بنجاح')\n    } catch (error) {\n      console.error('Error publishing project:', error)\n      toast.error('خطأ في نشر المشروع')\n    }\n  }\n\n  const getDeviceIcon = (device: string) => {\n    switch (device) {\n      case 'desktop': return <Monitor className=\"h-4 w-4\" />\n      case 'tablet': return <Tablet className=\"h-4 w-4\" />\n      case 'mobile': return <Smartphone className=\"h-4 w-4\" />\n      default: return <Monitor className=\"h-4 w-4\" />\n    }\n  }\n\n  const getDeviceWidth = (device: string) => {\n    switch (device) {\n      case 'desktop': return '100%'\n      case 'tablet': return '768px'\n      case 'mobile': return '375px'\n      default: return '100%'\n    }\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* شريط الأدوات */}\n      <div className=\"border-b bg-background p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <h2 className=\"text-xl font-semibold arabic-text\">\n              {currentProject ? currentProject.name : 'بناء الصفحات الذكية'}\n            </h2>\n            {currentProject && (\n              <Badge variant=\"outline\">\n                {currentProject.generationMode === 'ai' ? '🤖 مولد بالذكاء الاصطناعي' :\n                 currentProject.generationMode === 'template' ? '📋 من قالب' : '✏️ يدوي'}\n              </Badge>\n            )}\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            {/* أزرار المعاينة */}\n            <div className=\"flex items-center border rounded-lg\">\n              {(['desktop', 'tablet', 'mobile'] as const).map((device) => (\n                <Button\n                  key={device}\n                  variant={previewDevice === device ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setPreviewDevice(device)}\n                  className=\"rounded-none first:rounded-l-lg last:rounded-r-lg\"\n                >\n                  {getDeviceIcon(device)}\n                </Button>\n              ))}\n            </div>\n\n            {currentProject && (\n              <>\n                <Button variant=\"outline\" onClick={previewProject}>\n                  <Eye className=\"h-4 w-4 mr-2\" />\n                  معاينة\n                </Button>\n                <Button variant=\"outline\" onClick={saveProject}>\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  حفظ\n                </Button>\n                <Button onClick={publishProject}>\n                  <Upload className=\"h-4 w-4 mr-2\" />\n                  نشر\n                </Button>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex-1 flex\">\n        {/* الشريط الجانبي */}\n        <div className=\"w-80 border-r bg-muted/30 p-4 space-y-4\">\n          <div className=\"space-y-2\">\n            <Button \n              onClick={() => setShowAIDialog(true)} \n              className=\"w-full justify-start\"\n              variant=\"outline\"\n            >\n              <Wand2 className=\"h-4 w-4 mr-2\" />\n              توليد بالذكاء الاصطناعي\n            </Button>\n            \n            <Button \n              onClick={() => setShowTemplateDialog(true)} \n              className=\"w-full justify-start\"\n              variant=\"outline\"\n            >\n              <Layout className=\"h-4 w-4 mr-2\" />\n              اختيار قالب\n            </Button>\n            \n            <Button \n              onClick={() => createNewProject('مشروع جديد')} \n              className=\"w-full justify-start\"\n              variant=\"outline\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              مشروع فارغ\n            </Button>\n          </div>\n\n          {currentProject && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-sm\">تفاصيل المشروع</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-2 text-sm\">\n                <div>\n                  <Label>الاسم:</Label>\n                  <p className=\"text-muted-foreground\">{currentProject.name}</p>\n                </div>\n                <div>\n                  <Label>المكونات:</Label>\n                  <p className=\"text-muted-foreground\">{currentProject.components.length}</p>\n                </div>\n                <div>\n                  <Label>آخر تحديث:</Label>\n                  <p className=\"text-muted-foreground\">\n                    {new Date(currentProject.updatedAt).toLocaleDateString('ar-MA')}\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n\n        {/* منطقة المعاينة */}\n        <div className=\"flex-1 p-4 bg-gray-50 dark:bg-gray-900\">\n          {currentProject ? (\n            <div className=\"h-full flex items-center justify-center\">\n              <div \n                className=\"bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden\"\n                style={{ \n                  width: getDeviceWidth(previewDevice),\n                  height: '80vh',\n                  maxWidth: '100%'\n                }}\n              >\n                <div className=\"h-full overflow-y-auto\">\n                  {currentProject.components.length > 0 ? (\n                    <div className=\"space-y-0\">\n                      {currentProject.components.map((component, index) => (\n                        <div\n                          key={component.id}\n                          className=\"border-2 border-dashed border-transparent hover:border-blue-300 transition-colors\"\n                          style={{\n                            height: component.size.height,\n                            backgroundColor: component.props.style?.backgroundColor || '#f9fafb',\n                            color: component.props.style?.color || '#111827',\n                            padding: component.props.style?.padding || '1rem',\n                            textAlign: component.props.style?.textAlign || 'right'\n                          }}\n                        >\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <Badge variant=\"secondary\" className=\"text-xs\">\n                              {component.type}\n                            </Badge>\n                          </div>\n                          <div className=\"arabic-text\">\n                            {component.props.content || `مكون ${component.type}`}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"h-full flex items-center justify-center text-muted-foreground\">\n                      <div className=\"text-center\">\n                        <Layout className=\"h-12 w-12 mx-auto mb-4\" />\n                        <p>لا توجد مكونات بعد</p>\n                        <p className=\"text-sm\">ابدأ بإضافة مكونات للصفحة</p>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"h-full flex items-center justify-center\">\n              <div className=\"text-center\">\n                <Sparkles className=\"h-16 w-16 mx-auto mb-4 text-muted-foreground\" />\n                <h3 className=\"text-xl font-semibold mb-2\">مرحباً ببناء الصفحات الذكية</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  ابدأ بإنشاء صفحة جديدة باستخدام الذكاء الاصطناعي أو اختر قالباً جاهزاً\n                </p>\n                <div className=\"flex gap-2 justify-center\">\n                  <Button onClick={() => setShowAIDialog(true)}>\n                    <Wand2 className=\"h-4 w-4 mr-2\" />\n                    توليد بالذكاء الاصطناعي\n                  </Button>\n                  <Button variant=\"outline\" onClick={() => setShowTemplateDialog(true)}>\n                    <Layout className=\"h-4 w-4 mr-2\" />\n                    اختيار قالب\n                  </Button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* حوار التوليد بالذكاء الاصطناعي */}\n      <Dialog open={showAIDialog} onOpenChange={setShowAIDialog}>\n        <DialogContent className=\"max-w-2xl\">\n          <DialogHeader>\n            <DialogTitle className=\"arabic-text flex items-center gap-2\">\n              <Wand2 className=\"h-5 w-5\" />\n              توليد صفحة بالذكاء الاصطناعي\n            </DialogTitle>\n            <DialogDescription>\n              صف الصفحة التي تريد إنشاءها وسيقوم الذكاء الاصطناعي بتوليدها لك\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"aiPrompt\">وصف الصفحة *</Label>\n              <Textarea\n                id=\"aiPrompt\"\n                value={aiPrompt}\n                onChange={(e) => setAiPrompt(e.target.value)}\n                placeholder=\"مثال: أريد صفحة هبوط لشركة أزياء التخرج تتضمن قسم البطل وعرض المنتجات ونموذج اتصال...\"\n                rows={4}\n                className=\"arabic-text\"\n              />\n            </div>\n\n            <div className=\"bg-muted p-4 rounded-lg\">\n              <h4 className=\"font-medium mb-2\">نصائح للحصول على أفضل النتائج:</h4>\n              <ul className=\"text-sm text-muted-foreground space-y-1\">\n                <li>• كن محدداً في وصف نوع الصفحة (هبوط، منتج، شركة، إلخ)</li>\n                <li>• اذكر الأقسام المطلوبة (عن الشركة، المنتجات، الاتصال)</li>\n                <li>• حدد الألوان أو النمط المفضل إن أردت</li>\n                <li>• اذكر الجمهور المستهدف</li>\n              </ul>\n            </div>\n          </div>\n\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setShowAIDialog(false)}>\n              إلغاء\n            </Button>\n            <Button \n              onClick={generateWithAI} \n              disabled={isGenerating || !aiPrompt.trim()}\n            >\n              {isGenerating ? (\n                <>\n                  <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                  جاري التوليد...\n                </>\n              ) : (\n                <>\n                  <Zap className=\"h-4 w-4 mr-2\" />\n                  توليد الصفحة\n                </>\n              )}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* حوار اختيار القالب */}\n      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>\n        <DialogContent className=\"max-w-4xl max-h-[80vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle className=\"arabic-text\">اختيار قالب</DialogTitle>\n            <DialogDescription>\n              اختر قالباً جاهزاً لبدء مشروعك\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {templates.map((template) => (\n              <Card \n                key={template.id} \n                className=\"cursor-pointer hover:shadow-lg transition-shadow\"\n                onClick={() => useTemplate(template)}\n              >\n                <div className=\"aspect-video bg-muted rounded-t-lg flex items-center justify-center\">\n                  <Layout className=\"h-8 w-8 text-muted-foreground\" />\n                </div>\n                <CardContent className=\"p-4\">\n                  <h3 className=\"font-semibold arabic-text\">{template.nameAr}</h3>\n                  <p className=\"text-sm text-muted-foreground mt-1\">\n                    {template.description}\n                  </p>\n                  <div className=\"flex items-center justify-between mt-3\">\n                    <Badge variant=\"outline\">{template.category}</Badge>\n                    <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n                      <span>{template.usageCount}</span>\n                      <span>استخدام</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {templates.length === 0 && (\n            <div className=\"text-center py-8\">\n              <Layout className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground\" />\n              <p className=\"text-muted-foreground\">لا توجد قوالب متاحة حالياً</p>\n            </div>\n          )}\n\n          <DialogFooter>\n            <Button variant=\"outline\" onClick={() => setShowTemplateDialog(false)}>\n              إلغاء\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AACA;AACA;AACA;AASA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA3BA;;;;;;;;;;;AAoDO,SAAS,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAoB;IACrF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,WAAW;IACpF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAE9E,cAAc;IACd,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa,KAAK,SAAS;YAC7B,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,mBAAmB,OAAO,MAAc;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA,gBAAgB,aAAa,aAAa;gBAC5C;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,kBAAkB,KAAK,OAAO;gBAC9B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO;YAC5B,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,oBAAyC;gBAC7C,QAAQ;gBACR,UAAU;gBACV,eAAe;gBACf,aAAa;YACf;YAEA,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;gBAC/B,uCAAuC;gBACvC,MAAM,cAAc,CAAC,+BAA+B,EAAE,IAAI,OAAO,kBAAkB,CAAC,UAAU;gBAE9F,MAAM,aAA0B;oBAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB,MAAM;oBACN,aAAa,CAAC,SAAS,EAAE,UAAU;oBACnC,YAAY,KAAK,UAAU,IAAI,EAAE;oBACjC,gBAAgB;oBAChB,UAAU;wBACR,OAAO;wBACP,aAAa;wBACb,UAAU,EAAE;wBACZ,UAAU;wBACV,WAAW;oBACb;oBACA,aAAa;oBACb,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW;oBACX,SAAS;gBACX;gBAEA,kBAAkB;gBAClB,gBAAgB;gBAChB,YAAY;gBAEZ,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;oBACnD,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,KAAK,WAAW,CAAC,EAAE,EAAE;gBACvD;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,eAAe;IACf,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,MAAM,cAAc,CAAC,eAAe,EAAE,SAAS,MAAM,EAAE;YAEvD,MAAM,aAA0B;gBAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,aAAa,CAAC,eAAe,EAAE,SAAS,MAAM,EAAE;gBAChD,YAAY,SAAS,UAAU;gBAC/B,YAAY,SAAS,EAAE;gBACvB,gBAAgB;gBAChB,UAAU;oBACR,OAAO;oBACP,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,IAAI;oBACvB,UAAU;oBACV,WAAW;gBACb;gBACA,aAAa;gBACb,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW;gBACX,SAAS;YACX;YAEA,kBAAkB;YAClB,sBAAsB;YAEtB,gCAAgC;YAChC,MAAM,MAAM,+BAA+B;gBACzC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,SAAS,EAAE;oBACvB,QAAQ;gBACV;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,cAAc;IACd,MAAM,cAAc;QAClB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,SAAS;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB;QACrB,YAAY;IACd;IAEA,cAAc;IACd,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,YAAY;YAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC1C,KAAK;gBAAU,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAU,qBAAO,8OAAC,8MAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC5C;gBAAS,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QACrC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,iBAAiB,eAAe,IAAI,GAAG;;;;;;gCAEzC,gCACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CACZ,eAAe,cAAc,KAAK,OAAO,8BACzC,eAAe,cAAc,KAAK,aAAa,eAAe;;;;;;;;;;;;sCAKrE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,AAAC;wCAAC;wCAAW;wCAAU;qCAAS,CAAW,GAAG,CAAC,CAAC,uBAC/C,8OAAC,kIAAA,CAAA,SAAM;4CAEL,SAAS,kBAAkB,SAAS,YAAY;4CAChD,MAAK;4CACL,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDAET,cAAc;2CANV;;;;;;;;;;gCAWV,gCACC;;sDACE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;;8DACjC,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGlC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;;8DACjC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;;8DACf,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;wCACV,SAAQ;;0DAER,8OAAC,+MAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;wCACV,SAAQ;;0DAER,8OAAC,qNAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIrC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,iBAAiB;wCAChC,WAAU;wCACV,SAAQ;;0DAER,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAKpC,gCACC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;;;;;;kDAEjC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAyB,eAAe,IAAI;;;;;;;;;;;;0DAE3D,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEAAyB,eAAe,UAAU,CAAC,MAAM;;;;;;;;;;;;0DAExE,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,eAAe,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASnE,8OAAC;wBAAI,WAAU;kCACZ,+BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,eAAe;oCACtB,QAAQ;oCACR,UAAU;gCACZ;0CAEA,cAAA,8OAAC;oCAAI,WAAU;8CACZ,eAAe,UAAU,CAAC,MAAM,GAAG,kBAClC,8OAAC;wCAAI,WAAU;kDACZ,eAAe,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACzC,8OAAC;gDAEC,WAAU;gDACV,OAAO;oDACL,QAAQ,UAAU,IAAI,CAAC,MAAM;oDAC7B,iBAAiB,UAAU,KAAK,CAAC,KAAK,EAAE,mBAAmB;oDAC3D,OAAO,UAAU,KAAK,CAAC,KAAK,EAAE,SAAS;oDACvC,SAAS,UAAU,KAAK,CAAC,KAAK,EAAE,WAAW;oDAC3C,WAAW,UAAU,KAAK,CAAC,KAAK,EAAE,aAAa;gDACjD;;kEAEA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAClC,UAAU,IAAI;;;;;;;;;;;kEAGnB,8OAAC;wDAAI,WAAU;kEACZ,UAAU,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,EAAE,UAAU,IAAI,EAAE;;;;;;;+CAhBjD,UAAU,EAAE;;;;;;;;;6DAsBvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,qNAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAE;;;;;;8DACH,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAQnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS,IAAM,gBAAgB;;kEACrC,8OAAC,+MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,sBAAsB;;kEAC7D,8OAAC,qNAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAc,cAAc;0BACxC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,+MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,aAAY;4CACZ,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAKV,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,gBAAgB;8CAAQ;;;;;;8CAGjE,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,gBAAgB,CAAC,SAAS,IAAI;8CAEvC,6BACC;;0DACE,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAA8B;;qEAIrD;;0DACE,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAoB,cAAc;0BAC9C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;8CAAc;;;;;;8CACrC,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,gIAAA,CAAA,OAAI;oCAEH,WAAU;oCACV,SAAS,IAAM,YAAY;;sDAE3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qNAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;8DAA6B,SAAS,MAAM;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;8DAEvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW,SAAS,QAAQ;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAM,SAAS,UAAU;;;;;;8EAC1B,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;mCAhBP,SAAS,EAAE;;;;;;;;;;wBAwBrB,UAAU,MAAM,KAAK,mBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qNAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIzC,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,sBAAsB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnF", "debugId": null}}, {"offset": {"line": 1773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/dashboard/admin/page-builder/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { AdminDashboardHeader } from '@/components/admin/AdminDashboardHeader'\nimport { PageBuilder } from '@/components/admin/PageBuilder'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { toast } from 'sonner'\nimport { \n  Wand2, \n  Layout, \n  Plus, \n  Eye, \n  Edit, \n  Trash2, \n  Upload,\n  Download,\n  BarChart3,\n  RefreshCw,\n  Sparkles,\n  Globe,\n  FileText\n} from 'lucide-react'\nimport { PageProject } from '@/types/page-builder'\n\nexport default function PageBuilderPage() {\n  const { user, profile } = useAuth()\n  const [projects, setProjects] = useState<PageProject[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showBuilder, setShowBuilder] = useState(false)\n  const [currentProject, setCurrentProject] = useState<PageProject | undefined>()\n  const [previewProject, setPreviewProject] = useState<PageProject | undefined>()\n  const [showPreview, setShowPreview] = useState(false)\n  const [stats, setStats] = useState<any>({})\n\n  // جلب المشاريع\n  const fetchProjects = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/page-builder?include_unpublished=true')\n      const data = await response.json()\n      \n      if (response.ok) {\n        setProjects(data.projects)\n        setStats(data.stats)\n      } else {\n        toast.error(data.error || 'خطأ في جلب المشاريع')\n      }\n    } catch (error) {\n      console.error('Error fetching projects:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchProjects()\n  }, [])\n\n  // إنشاء مشروع جديد\n  const createNewProject = () => {\n    setCurrentProject(undefined)\n    setShowBuilder(true)\n  }\n\n  // تحرير مشروع\n  const editProject = (project: PageProject) => {\n    setCurrentProject(project)\n    setShowBuilder(true)\n  }\n\n  // حفظ مشروع\n  const saveProject = async (project: PageProject) => {\n    try {\n      const url = project.id && projects.find(p => p.id === project.id) \n        ? `/api/page-builder/${project.id}` \n        : '/api/page-builder'\n      const method = project.id && projects.find(p => p.id === project.id) ? 'PUT' : 'POST'\n      \n      const response = await fetch(url, {\n        method,\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(project)\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        toast.success(result.message)\n        fetchProjects()\n        setShowBuilder(false)\n      } else {\n        toast.error(result.error)\n      }\n    } catch (error) {\n      console.error('Error saving project:', error)\n      toast.error('خطأ في حفظ المشروع')\n    }\n  }\n\n  // معاينة مشروع\n  const handlePreview = (project: PageProject) => {\n    setPreviewProject(project)\n    setShowPreview(true)\n  }\n\n  // نشر مشروع\n  const publishProject = async (project: PageProject) => {\n    try {\n      const response = await fetch('/api/page-builder', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'publish',\n          projectIds: [project.id]\n        })\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        toast.success('تم نشر المشروع بنجاح')\n        fetchProjects()\n      } else {\n        toast.error(result.error)\n      }\n    } catch (error) {\n      console.error('Error publishing project:', error)\n      toast.error('خطأ في نشر المشروع')\n    }\n  }\n\n  // حذف مشروع\n  const deleteProject = async (projectId: string) => {\n    try {\n      const response = await fetch(`/api/page-builder?ids=${projectId}`, {\n        method: 'DELETE'\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        toast.success(result.message)\n        fetchProjects()\n      } else {\n        toast.error(result.error)\n      }\n    } catch (error) {\n      console.error('Error deleting project:', error)\n      toast.error('خطأ في حذف المشروع')\n    }\n  }\n\n  const getGenerationModeIcon = (mode: string) => {\n    switch (mode) {\n      case 'ai': return <Sparkles className=\"h-4 w-4\" />\n      case 'template': return <Layout className=\"h-4 w-4\" />\n      case 'manual': return <Edit className=\"h-4 w-4\" />\n      default: return <FileText className=\"h-4 w-4\" />\n    }\n  }\n\n  const getGenerationModeLabel = (mode: string) => {\n    switch (mode) {\n      case 'ai': return 'ذكاء اصطناعي'\n      case 'template': return 'من قالب'\n      case 'manual': return 'يدوي'\n      default: return 'غير محدد'\n    }\n  }\n\n  if (showBuilder) {\n    return (\n      <ProtectedRoute requiredRole={UserRole.ADMIN}>\n        <div className=\"h-screen flex flex-col\">\n          <AdminDashboardHeader />\n          <div className=\"flex-1\">\n            <PageBuilder\n              project={currentProject}\n              onSave={saveProject}\n              onPreview={handlePreview}\n              onPublish={publishProject}\n            />\n          </div>\n          <div className=\"border-t p-4 bg-background\">\n            <Button \n              variant=\"outline\" \n              onClick={() => setShowBuilder(false)}\n            >\n              العودة للمشاريع\n            </Button>\n          </div>\n        </div>\n      </ProtectedRoute>\n    )\n  }\n\n  return (\n    <ProtectedRoute requiredRole={UserRole.ADMIN}>\n      <div className=\"min-h-screen bg-background\">\n        <AdminDashboardHeader />\n        \n        <div className=\"container mx-auto px-4 py-8\">\n          {/* العنوان والإحصائيات */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div>\n              <h1 className=\"text-3xl font-bold arabic-text flex items-center gap-3\">\n                <Wand2 className=\"h-8 w-8 text-primary\" />\n                بناء الصفحات الذكية\n              </h1>\n              <p className=\"text-muted-foreground mt-2\">\n                إنشاء وإدارة صفحات الويب باستخدام الذكاء الاصطناعي\n              </p>\n            </div>\n            \n            <div className=\"flex gap-2\">\n              <Button onClick={() => fetchProjects()} variant=\"outline\">\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                تحديث\n              </Button>\n              <Button onClick={createNewProject}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                مشروع جديد\n              </Button>\n            </div>\n          </div>\n\n          {/* بطاقات الإحصائيات */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">إجمالي المشاريع</CardTitle>\n                <FileText className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.total || 0}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  {stats.published || 0} منشور، {stats.unpublished || 0} مسودة\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">مولد بالذكاء الاصطناعي</CardTitle>\n                <Sparkles className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.byGenerationMode?.ai || 0}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  من إجمالي المشاريع\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">من القوالب</CardTitle>\n                <Layout className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.byGenerationMode?.template || 0}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  مبني على قوالب جاهزة\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">المنشورة</CardTitle>\n                <Globe className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.published || 0}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  متاحة للعامة\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* قائمة المشاريع */}\n          {loading ? (\n            <div className=\"text-center py-12\">\n              <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n              <p>جاري تحميل المشاريع...</p>\n            </div>\n          ) : projects.length === 0 ? (\n            <Card>\n              <CardContent className=\"text-center py-12\">\n                <Wand2 className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground\" />\n                <h3 className=\"text-lg font-semibold mb-2\">لا توجد مشاريع</h3>\n                <p className=\"text-muted-foreground mb-4\">\n                  ابدأ بإنشاء مشروعك الأول باستخدام الذكاء الاصطناعي\n                </p>\n                <Button onClick={createNewProject}>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  إنشاء مشروع جديد\n                </Button>\n              </CardContent>\n            </Card>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {projects.map((project) => (\n                <Card key={project.id} className=\"hover:shadow-lg transition-shadow\">\n                  <CardHeader>\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <CardTitle className=\"text-lg arabic-text line-clamp-1\">\n                          {project.name}\n                        </CardTitle>\n                        <p className=\"text-sm text-muted-foreground mt-1 line-clamp-2\">\n                          {project.description}\n                        </p>\n                      </div>\n                      {project.isPublished && (\n                        <Badge variant=\"default\" className=\"ml-2\">\n                          <Globe className=\"h-3 w-3 mr-1\" />\n                          منشور\n                        </Badge>\n                      )}\n                    </div>\n                  </CardHeader>\n                  \n                  <CardContent className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <div className=\"flex items-center gap-2\">\n                        {getGenerationModeIcon(project.generationMode)}\n                        <span>{getGenerationModeLabel(project.generationMode)}</span>\n                      </div>\n                      <span className=\"text-muted-foreground\">\n                        {project.components.length} مكون\n                      </span>\n                    </div>\n\n                    <div className=\"text-xs text-muted-foreground\">\n                      آخر تحديث: {new Date(project.updatedAt).toLocaleDateString('ar-MA')}\n                    </div>\n\n                    <div className=\"flex gap-2\">\n                      <Button \n                        size=\"sm\" \n                        variant=\"outline\" \n                        onClick={() => handlePreview(project)}\n                        className=\"flex-1\"\n                      >\n                        <Eye className=\"h-4 w-4 mr-2\" />\n                        معاينة\n                      </Button>\n                      <Button \n                        size=\"sm\" \n                        variant=\"outline\" \n                        onClick={() => editProject(project)}\n                        className=\"flex-1\"\n                      >\n                        <Edit className=\"h-4 w-4 mr-2\" />\n                        تحرير\n                      </Button>\n                      <Button \n                        size=\"sm\" \n                        variant=\"outline\" \n                        onClick={() => deleteProject(project.id)}\n                        className=\"text-destructive hover:text-destructive\"\n                      >\n                        <Trash2 className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n\n                    {!project.isPublished && (\n                      <Button \n                        size=\"sm\" \n                        onClick={() => publishProject(project)}\n                        className=\"w-full\"\n                      >\n                        <Upload className=\"h-4 w-4 mr-2\" />\n                        نشر المشروع\n                      </Button>\n                    )}\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* حوار المعاينة */}\n        <Dialog open={showPreview} onOpenChange={setShowPreview}>\n          <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\n            <DialogHeader>\n              <DialogTitle className=\"arabic-text\">\n                معاينة: {previewProject?.name}\n              </DialogTitle>\n              <DialogDescription>\n                معاينة الصفحة كما ستظهر للزوار\n              </DialogDescription>\n            </DialogHeader>\n\n            {previewProject && (\n              <div className=\"bg-white rounded-lg overflow-hidden border\">\n                <div className=\"space-y-0\">\n                  {previewProject.components.map((component) => (\n                    <div\n                      key={component.id}\n                      style={{\n                        height: component.size.height,\n                        backgroundColor: component.props.style?.backgroundColor || '#f9fafb',\n                        color: component.props.style?.color || '#111827',\n                        padding: component.props.style?.padding || '1rem',\n                        textAlign: component.props.style?.textAlign || 'right'\n                      }}\n                      className=\"arabic-text\"\n                    >\n                      {component.props.content || `مكون ${component.type}`}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </DialogContent>\n        </Dialog>\n      </div>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AApBA;;;;;;;;;;;;;;AAqCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAEzC,eAAe;IACf,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,KAAK,QAAQ;gBACzB,SAAS,KAAK,KAAK;YACrB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,eAAe;IACjB;IAEA,cAAc;IACd,MAAM,cAAc,CAAC;QACnB,kBAAkB;QAClB,eAAe;IACjB;IAEA,YAAY;IACZ,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,MAAM,MAAM,QAAQ,EAAE,IAAI,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE,IAC5D,CAAC,kBAAkB,EAAE,QAAQ,EAAE,EAAE,GACjC;YACJ,MAAM,SAAS,QAAQ,EAAE,IAAI,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE,IAAI,QAAQ;YAE/E,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B;gBACA,eAAe;YACjB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,eAAe;IACf,MAAM,gBAAgB,CAAC;QACrB,kBAAkB;QAClB,eAAe;IACjB;IAEA,YAAY;IACZ,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,YAAY;wBAAC,QAAQ,EAAE;qBAAC;gBAC1B;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,YAAY;IACZ,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,WAAW,EAAE;gBACjE,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAM,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YACtC,KAAK;gBAAY,qBAAO,8OAAC,qNAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC1C,KAAK;gBAAU,qBAAO,8OAAC,2MAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACtC;gBAAS,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QACtC;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC,4IAAA,CAAA,iBAAc;YAAC,cAAc,oHAAA,CAAA,WAAQ,CAAC,KAAK;sBAC1C,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,mJAAA,CAAA,uBAAoB;;;;;kCACrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0IAAA,CAAA,cAAW;4BACV,SAAS;4BACT,QAAQ;4BACR,WAAW;4BACX,WAAW;;;;;;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,eAAe;sCAC/B;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC,4IAAA,CAAA,iBAAc;QAAC,cAAc,oHAAA,CAAA,WAAQ,CAAC,KAAK;kBAC1C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,mJAAA,CAAA,uBAAoB;;;;;8BAErB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,+MAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAG5C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM;4CAAiB,SAAQ;;8DAC9C,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;;8DACf,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,MAAM,KAAK,IAAI;;;;;;8DACpD,8OAAC;oDAAE,WAAU;;wDACV,MAAM,SAAS,IAAI;wDAAE;wDAAS,MAAM,WAAW,IAAI;wDAAE;;;;;;;;;;;;;;;;;;;8CAK5D,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,MAAM,gBAAgB,EAAE,MAAM;;;;;;8DACnE,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,qNAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;sDAEpB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,MAAM,gBAAgB,EAAE,YAAY;;;;;;8DACzE,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,MAAM,SAAS,IAAI;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;wBAQlD,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAE;;;;;;;;;;;mCAEH,SAAS,MAAM,KAAK,kBACtB,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,+MAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;iDAMvC,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,gIAAA,CAAA,OAAI;oCAAkB,WAAU;;sDAC/B,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,QAAQ,IAAI;;;;;;0EAEf,8OAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;oDAGvB,QAAQ,WAAW,kBAClB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;0EACjC,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;sDAO1C,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,sBAAsB,QAAQ,cAAc;8EAC7C,8OAAC;8EAAM,uBAAuB,QAAQ,cAAc;;;;;;;;;;;;sEAEtD,8OAAC;4DAAK,WAAU;;gEACb,QAAQ,UAAU,CAAC,MAAM;gEAAC;;;;;;;;;;;;;8DAI/B,8OAAC;oDAAI,WAAU;;wDAAgC;wDACjC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC;;;;;;;8DAG7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,cAAc;4DAC7B,WAAU;;8EAEV,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGlC,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,YAAY;4DAC3B,WAAU;;8EAEV,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,cAAc,QAAQ,EAAE;4DACvC,WAAU;sEAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAIrB,CAAC,QAAQ,WAAW,kBACnB,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,eAAe;oDAC9B,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;mCAtEhC,QAAQ,EAAE;;;;;;;;;;;;;;;;8BAkF7B,8OAAC,kIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAa,cAAc;8BACvC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,cAAW;wCAAC,WAAU;;4CAAc;4CAC1B,gBAAgB;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,oBAAiB;kDAAC;;;;;;;;;;;;4BAKpB,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,eAAe,UAAU,CAAC,GAAG,CAAC,CAAC,0BAC9B,8OAAC;4CAEC,OAAO;gDACL,QAAQ,UAAU,IAAI,CAAC,MAAM;gDAC7B,iBAAiB,UAAU,KAAK,CAAC,KAAK,EAAE,mBAAmB;gDAC3D,OAAO,UAAU,KAAK,CAAC,KAAK,EAAE,SAAS;gDACvC,SAAS,UAAU,KAAK,CAAC,KAAK,EAAE,WAAW;gDAC3C,WAAW,UAAU,KAAK,CAAC,KAAK,EAAE,aAAa;4CACjD;4CACA,WAAU;sDAET,UAAU,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,EAAE,UAAU,IAAI,EAAE;2CAV/C,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBvC", "debugId": null}}]}