import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'

// POST - فحص حالة API لمقدم خدمة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { provider, baseUrl, apiKey, selectedModels } = body

    // التحقق من البيانات المطلوبة
    if (!provider || !baseUrl) {
      return NextResponse.json(
        { error: 'مقدم الخدمة و Base URL مطلوبان' },
        { status: 400 }
      )
    }

    // محاكاة فحص الاتصال
    const healthCheckResult = await performHealthCheck(provider, baseUrl, apiKey, selectedModels)

    return NextResponse.json(healthCheckResult)

  } catch (error) {
    console.error('Error performing health check:', error)
    return NextResponse.json(
      { error: 'خطأ في فحص حالة API' },
      { status: 500 }
    )
  }
}

// GET - فحص حالة جميع النماذج المحفوظة
export async function GET(request: NextRequest) {
  try {
    const models = MockDataManager.getAIModels()
    const healthResults = []

    for (const model of models) {
      if (model.isActive && model.baseUrl) {
        const result = await performHealthCheck(
          model.provider, 
          model.baseUrl, 
          model.apiKey, 
          model.selectedModels || []
        )
        
        healthResults.push({
          modelId: model.id,
          provider: model.provider,
          name: model.name,
          ...result
        })

        // تحديث حالة النموذج في قاعدة البيانات
        const modelIndex = models.findIndex(m => m.id === model.id)
        if (modelIndex !== -1) {
          models[modelIndex].status = result.status
          models[modelIndex].lastCheckedAt = new Date().toISOString()
          models[modelIndex].healthCheck = result
        }
      }
    }

    // حفظ التحديثات
    MockDataManager.saveAIModels(models)

    return NextResponse.json({
      results: healthResults,
      summary: {
        total: healthResults.length,
        healthy: healthResults.filter(r => r.status === 'active').length,
        unhealthy: healthResults.filter(r => r.status === 'error').length,
        warning: healthResults.filter(r => r.status === 'warning').length
      }
    })

  } catch (error) {
    console.error('Error performing bulk health check:', error)
    return NextResponse.json(
      { error: 'خطأ في فحص حالة النماذج' },
      { status: 500 }
    )
  }
}

// دالة محاكاة فحص الصحة
async function performHealthCheck(provider: string, baseUrl: string, apiKey?: string, selectedModels: string[] = []) {
  const startTime = Date.now()
  
  try {
    // محاكاة وقت الاستجابة بناءً على مقدم الخدمة
    const responseTime = await simulateApiCall(provider, baseUrl, apiKey)
    
    // محاكاة فحص النماذج المحددة
    const modelResults = await checkSelectedModels(provider, selectedModels, apiKey)
    
    // تحديد الحالة العامة
    const overallStatus = determineOverallStatus(modelResults, responseTime)
    
    return {
      status: overallStatus,
      responseTime,
      timestamp: new Date().toISOString(),
      baseUrl,
      hasApiKey: !!apiKey,
      models: modelResults,
      details: {
        endpoint: baseUrl,
        provider,
        modelsChecked: selectedModels.length,
        modelsHealthy: modelResults.filter(m => m.status === 'healthy').length,
        averageResponseTime: responseTime
      }
    }
    
  } catch (error) {
    return {
      status: 'error',
      responseTime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
      baseUrl,
      hasApiKey: !!apiKey,
      error: error instanceof Error ? error.message : 'خطأ غير معروف',
      models: [],
      details: {
        endpoint: baseUrl,
        provider,
        modelsChecked: 0,
        modelsHealthy: 0,
        averageResponseTime: 0
      }
    }
  }
}

// محاكاة استدعاء API
async function simulateApiCall(provider: string, baseUrl: string, apiKey?: string): Promise<number> {
  const startTime = Date.now()
  
  // محاكاة أوقات استجابة مختلفة لمقدمي الخدمة
  const providerResponseTimes = {
    'openai': { min: 500, max: 2000 },
    'anthropic': { min: 800, max: 3000 },
    'google': { min: 400, max: 1500 },
    'meta': { min: 1000, max: 4000 },
    'stability': { min: 2000, max: 8000 },
    'cohere': { min: 600, max: 2500 },
    'huggingface': { min: 1500, max: 5000 },
    'deepseek': { min: 700, max: 2800 }
  }
  
  const timing = providerResponseTimes[provider as keyof typeof providerResponseTimes] || { min: 1000, max: 3000 }
  const simulatedDelay = Math.floor(Math.random() * (timing.max - timing.min)) + timing.min
  
  // محاكاة التأخير
  await new Promise(resolve => setTimeout(resolve, Math.min(simulatedDelay, 1000))) // حد أقصى ثانية واحدة للاختبار
  
  // محاكاة نسبة نجاح عالية (95%)
  if (Math.random() < 0.05) {
    throw new Error(`فشل في الاتصال بـ ${provider}`)
  }
  
  // محاكاة فحص صحة URL
  if (!baseUrl.startsWith('http')) {
    throw new Error('Base URL غير صالح')
  }
  
  // محاكاة فحص مفتاح API (إذا كان مطلوباً)
  if (provider === 'openai' && !apiKey) {
    throw new Error('مفتاح API مطلوب لـ OpenAI')
  }
  
  return Date.now() - startTime
}

// فحص النماذج المحددة
async function checkSelectedModels(provider: string, selectedModels: string[], apiKey?: string) {
  const results = []
  
  for (const modelName of selectedModels) {
    // محاكاة فحص كل نموذج
    const isHealthy = Math.random() > 0.1 // 90% نجاح
    const responseTime = Math.floor(Math.random() * 1000) + 200
    
    results.push({
      name: modelName,
      status: isHealthy ? 'healthy' : 'error',
      responseTime,
      available: isHealthy,
      error: isHealthy ? undefined : `النموذج ${modelName} غير متاح حالياً`
    })
  }
  
  return results
}

// تحديد الحالة العامة
function determineOverallStatus(modelResults: any[], responseTime: number) {
  const healthyModels = modelResults.filter(m => m.status === 'healthy').length
  const totalModels = modelResults.length
  
  if (totalModels === 0) {
    return responseTime > 5000 ? 'warning' : 'active'
  }
  
  const healthyPercentage = (healthyModels / totalModels) * 100
  
  if (healthyPercentage >= 90) {
    return responseTime > 5000 ? 'warning' : 'active'
  } else if (healthyPercentage >= 50) {
    return 'warning'
  } else {
    return 'error'
  }
}

// PUT - تحديث حالة نموذج يدوياً
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { modelId, status, notes } = body

    if (!modelId || !status) {
      return NextResponse.json(
        { error: 'معرف النموذج والحالة مطلوبان' },
        { status: 400 }
      )
    }

    const models = MockDataManager.getAIModels()
    const modelIndex = models.findIndex(m => m.id === modelId)

    if (modelIndex === -1) {
      return NextResponse.json(
        { error: 'النموذج غير موجود' },
        { status: 404 }
      )
    }

    // تحديث حالة النموذج
    models[modelIndex].status = status
    models[modelIndex].lastCheckedAt = new Date().toISOString()
    models[modelIndex].healthCheck = {
      status,
      timestamp: new Date().toISOString(),
      manual: true,
      notes
    }

    MockDataManager.saveAIModels(models)

    // إضافة نشاط
    const activities = MockDataManager.getModelActivities()
    activities.push({
      id: MockDataManager.generateId(),
      modelId,
      type: 'health_check',
      description: `تم تحديث حالة النموذج يدوياً إلى: ${status}`,
      timestamp: new Date().toISOString(),
      success: true,
      details: { notes, manual: true }
    })
    MockDataManager.saveModelActivities(activities)

    return NextResponse.json({
      message: 'تم تحديث حالة النموذج بنجاح',
      model: models[modelIndex]
    })

  } catch (error) {
    console.error('Error updating model status:', error)
    return NextResponse.json(
      { error: 'خطأ في تحديث حالة النموذج' },
      { status: 500 }
    )
  }
}
