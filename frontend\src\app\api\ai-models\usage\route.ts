import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'
import { ModelUsageFilter } from '@/types/ai-models'

// GET - جلب إحصائيات استخدام النماذج
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const modelId = searchParams.get('model_id')
    const provider = searchParams.get('provider')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const type = searchParams.get('type')
    const groupBy = searchParams.get('group_by') || 'day' // day, week, month
    const includeDetails = searchParams.get('include_details') === 'true'

    // جلب البيانات
    const models = MockDataManager.getAIModels()
    const activities = MockDataManager.getModelActivities()

    // فلترة النماذج
    let filteredModels = models
    if (modelId) {
      filteredModels = models.filter(m => m.id === modelId)
    }
    if (provider) {
      filteredModels = filteredModels.filter(m => m.provider === provider)
    }
    if (type) {
      filteredModels = filteredModels.filter(m => m.type === type)
    }

    // فلترة الأنشطة
    let filteredActivities = activities.filter(a => a.type === 'request')
    if (modelId) {
      filteredActivities = filteredActivities.filter(a => a.modelId === modelId)
    }
    if (dateFrom) {
      filteredActivities = filteredActivities.filter(a => a.timestamp >= dateFrom)
    }
    if (dateTo) {
      filteredActivities = filteredActivities.filter(a => a.timestamp <= dateTo)
    }

    // حساب الإحصائيات العامة
    const totalUsage = {
      totalRequests: filteredModels.reduce((sum, m) => sum + m.usage.totalRequests, 0),
      totalTokens: filteredModels.reduce((sum, m) => sum + m.usage.totalTokens, 0),
      totalCost: filteredModels.reduce((sum, m) => sum + m.usage.totalCost, 0),
      averageResponseTime: filteredModels.length > 0 
        ? Math.round(filteredModels.reduce((sum, m) => sum + m.usage.averageResponseTime, 0) / filteredModels.length)
        : 0,
      successRate: filteredModels.length > 0 
        ? Math.round(filteredModels.reduce((sum, m) => sum + m.usage.successRate, 0) / filteredModels.length)
        : 0
    }

    // إحصائيات حسب المقدم
    const byProvider = filteredModels.reduce((acc, model) => {
      const provider = model.provider
      if (!acc[provider]) {
        acc[provider] = {
          models: 0,
          totalRequests: 0,
          totalTokens: 0,
          totalCost: 0,
          averageResponseTime: 0,
          successRate: 0
        }
      }
      acc[provider].models += 1
      acc[provider].totalRequests += model.usage.totalRequests
      acc[provider].totalTokens += model.usage.totalTokens
      acc[provider].totalCost += model.usage.totalCost
      acc[provider].averageResponseTime += model.usage.averageResponseTime
      acc[provider].successRate += model.usage.successRate
      return acc
    }, {} as Record<string, any>)

    // حساب المتوسطات
    Object.keys(byProvider).forEach(provider => {
      const data = byProvider[provider]
      data.averageResponseTime = Math.round(data.averageResponseTime / data.models)
      data.successRate = Math.round(data.successRate / data.models)
    })

    // إحصائيات حسب النوع
    const byType = filteredModels.reduce((acc, model) => {
      const type = model.type
      if (!acc[type]) {
        acc[type] = {
          models: 0,
          totalRequests: 0,
          totalTokens: 0,
          totalCost: 0
        }
      }
      acc[type].models += 1
      acc[type].totalRequests += model.usage.totalRequests
      acc[type].totalTokens += model.usage.totalTokens
      acc[type].totalCost += model.usage.totalCost
      return acc
    }, {} as Record<string, any>)

    // إحصائيات زمنية (محاكاة بيانات يومية)
    const timeSeriesData = generateTimeSeriesData(groupBy, dateFrom, dateTo, filteredActivities)

    // أفضل النماذج أداءً
    const topModels = filteredModels
      .filter(m => m.usage.totalRequests > 0)
      .sort((a, b) => b.usage.totalRequests - a.usage.totalRequests)
      .slice(0, 10)
      .map(model => ({
        id: model.id,
        name: model.name,
        provider: model.provider,
        type: model.type,
        totalRequests: model.usage.totalRequests,
        totalCost: model.usage.totalCost,
        successRate: model.usage.successRate,
        averageResponseTime: model.usage.averageResponseTime,
        lastUsed: model.usage.lastUsed
      }))

    // النماذج الأكثر تكلفة
    const mostExpensive = filteredModels
      .filter(m => m.usage.totalCost > 0)
      .sort((a, b) => b.usage.totalCost - a.usage.totalCost)
      .slice(0, 5)
      .map(model => ({
        id: model.id,
        name: model.name,
        provider: model.provider,
        totalCost: model.usage.totalCost,
        totalRequests: model.usage.totalRequests,
        costPerRequest: model.usage.totalRequests > 0 
          ? model.usage.totalCost / model.usage.totalRequests 
          : 0
      }))

    // تحليل الاتجاهات
    const trends = analyzeTrends(timeSeriesData)

    // إعداد الاستجابة
    const response: any = {
      summary: totalUsage,
      byProvider,
      byType,
      timeSeries: timeSeriesData,
      topModels,
      mostExpensive,
      trends,
      period: {
        from: dateFrom || 'all-time',
        to: dateTo || new Date().toISOString(),
        groupBy
      }
    }

    // إضافة التفاصيل إذا طُلبت
    if (includeDetails) {
      response.models = filteredModels.map(model => ({
        id: model.id,
        name: model.name,
        provider: model.provider,
        type: model.type,
        status: model.status,
        usage: model.usage,
        lastTestedAt: model.lastTestedAt,
        testResult: model.testResult
      }))

      response.recentActivities = filteredActivities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 50)
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching usage statistics:', error)
    return NextResponse.json(
      { error: 'خطأ في جلب إحصائيات الاستخدام' },
      { status: 500 }
    )
  }
}

// دالة مساعدة لتوليد بيانات السلاسل الزمنية
function generateTimeSeriesData(groupBy: string, dateFrom?: string | null, dateTo?: string | null, activities: any[]) {
  const now = new Date()
  const endDate = dateTo ? new Date(dateTo) : now
  const startDate = dateFrom ? new Date(dateFrom) : new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000)) // آخر 30 يوم

  const data = []
  const current = new Date(startDate)

  while (current <= endDate) {
    const dateStr = current.toISOString().split('T')[0]
    
    // محاكاة بيانات عشوائية للتطوير
    const requests = Math.floor(Math.random() * 100) + 10
    const tokens = requests * (Math.floor(Math.random() * 500) + 100)
    const cost = tokens * 0.001 * (Math.random() * 0.01 + 0.001)
    const errors = Math.floor(Math.random() * 5)

    data.push({
      date: dateStr,
      requests,
      tokens,
      cost: Math.round(cost * 100) / 100,
      errors,
      successRate: Math.round(((requests - errors) / requests) * 100)
    })

    // الانتقال للفترة التالية
    if (groupBy === 'day') {
      current.setDate(current.getDate() + 1)
    } else if (groupBy === 'week') {
      current.setDate(current.getDate() + 7)
    } else if (groupBy === 'month') {
      current.setMonth(current.getMonth() + 1)
    }
  }

  return data
}

// دالة مساعدة لتحليل الاتجاهات
function analyzeTrends(timeSeriesData: any[]) {
  if (timeSeriesData.length < 2) {
    return {
      requests: 'stable',
      cost: 'stable',
      successRate: 'stable'
    }
  }

  const recent = timeSeriesData.slice(-7) // آخر 7 نقاط
  const previous = timeSeriesData.slice(-14, -7) // 7 نقاط قبلها

  const recentAvg = {
    requests: recent.reduce((sum, d) => sum + d.requests, 0) / recent.length,
    cost: recent.reduce((sum, d) => sum + d.cost, 0) / recent.length,
    successRate: recent.reduce((sum, d) => sum + d.successRate, 0) / recent.length
  }

  const previousAvg = {
    requests: previous.reduce((sum, d) => sum + d.requests, 0) / previous.length,
    cost: previous.reduce((sum, d) => sum + d.cost, 0) / previous.length,
    successRate: previous.reduce((sum, d) => sum + d.successRate, 0) / previous.length
  }

  const getTrend = (recent: number, previous: number) => {
    const change = ((recent - previous) / previous) * 100
    if (change > 10) return 'increasing'
    if (change < -10) return 'decreasing'
    return 'stable'
  }

  return {
    requests: getTrend(recentAvg.requests, previousAvg.requests),
    cost: getTrend(recentAvg.cost, previousAvg.cost),
    successRate: getTrend(recentAvg.successRate, previousAvg.successRate),
    changes: {
      requests: Math.round(((recentAvg.requests - previousAvg.requests) / previousAvg.requests) * 100),
      cost: Math.round(((recentAvg.cost - previousAvg.cost) / previousAvg.cost) * 100),
      successRate: Math.round(((recentAvg.successRate - previousAvg.successRate) / previousAvg.successRate) * 100)
    }
  }
}
