{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Loader2 } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: UserRole\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({\n  children,\n  requiredRole,\n  redirectTo = '/auth'\n}: ProtectedRouteProps) {\n  const { user, profile, loading, hasRole } = useAuth()\n  const router = useRouter()\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  useEffect(() => {\n    if (mounted && !loading) {\n      if (!user) {\n        router.push(redirectTo)\n        return\n      }\n\n      if (requiredRole && !hasRole(requiredRole)) {\n        router.push('/unauthorized')\n        return\n      }\n    }\n  }, [mounted, user, profile, loading, requiredRole, hasRole, router, redirectTo])\n\n  // Show loading until mounted and auth is resolved\n  if (!mounted || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\n            <Loader2 className=\"h-8 w-8 animate-spin text-blue-600 mb-4\" />\n            <p className=\"text-gray-600 dark:text-gray-300\">جاري التحميل...</p>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect\n  }\n\n  if (requiredRole && !hasRole(requiredRole)) {\n    return null // Will redirect\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;AAeO,SAAS,eAAe,EAC7B,QAAQ,EACR,YAAY,EACZ,aAAa,OAAO,EACA;;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW,CAAC,SAAS;gBACvB,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;oBAC1C,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;mCAAG;QAAC;QAAS;QAAM;QAAS;QAAS;QAAc;QAAS;QAAQ;KAAW;IAE/E,kDAAkD;IAClD,IAAI,CAAC,WAAW,SAAS;QACvB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;QAC1C,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,qBAAO;kBAAG;;AACZ;GAlDgB;;QAK8B,kIAAA,CAAA,UAAO;QACpC,qIAAA,CAAA,YAAS;;;KANV", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AdminQuickNav.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport {\n  Plus,\n  Menu,\n  Package,\n  Download,\n  FileText,\n  Users,\n  Settings\n} from 'lucide-react'\n\ninterface QuickNavItem {\n  label: string\n  href?: string\n  icon: React.ReactNode\n  variant?: 'default' | 'outline' | 'secondary'\n  disabled?: boolean\n}\n\nconst quickNavItems: QuickNavItem[] = [\n  {\n    label: 'إضافة صفحة',\n    href: '/dashboard/admin/pages-management',\n    icon: <Plus className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline'\n  },\n  {\n    label: 'تحرير القائمة',\n    href: '/dashboard/admin/menu-management',\n    icon: <Menu className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline'\n  },\n  {\n    label: 'إدارة المنتجات',\n    href: '/dashboard/admin/products',\n    icon: <Package className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline'\n  },\n  {\n    label: 'تصدير التقارير',\n    icon: <Download className=\"h-4 w-4 mr-2\" />,\n    variant: 'outline',\n    disabled: true\n  }\n]\n\nexport function AdminQuickNav() {\n  return (\n    <div className=\"flex gap-3 flex-wrap\">\n      {quickNavItems.map((item, index) => {\n        const ButtonComponent = (\n          <Button \n            key={index}\n            variant={item.variant || 'outline'} \n            size=\"sm\"\n            disabled={item.disabled}\n            className=\"arabic-text\"\n          >\n            {item.icon}\n            {item.label}\n          </Button>\n        )\n\n        return item.href && !item.disabled ? (\n          <Link key={index} href={item.href}>\n            {ButtonComponent}\n          </Link>\n        ) : (\n          ButtonComponent\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAsBA,MAAM,gBAAgC;IACpC;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,SAAS;IACX;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,SAAS;IACX;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,2MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,SAAS;IACX;IACA;QACE,OAAO;QACP,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,SAAS;QACT,UAAU;IACZ;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC,MAAM;YACxB,MAAM,gCACJ,6LAAC,qIAAA,CAAA,SAAM;gBAEL,SAAS,KAAK,OAAO,IAAI;gBACzB,MAAK;gBACL,UAAU,KAAK,QAAQ;gBACvB,WAAU;;oBAET,KAAK,IAAI;oBACT,KAAK,KAAK;;eAPN;;;;;YAWT,OAAO,KAAK,IAAI,IAAI,CAAC,KAAK,QAAQ,iBAChC,6LAAC,+JAAA,CAAA,UAAI;gBAAa,MAAM,KAAK,IAAI;0BAC9B;eADQ;;;;uBAIX;QAEJ;;;;;;AAGN;KA3BgB", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AdminDashboardHeader.tsx"], "sourcesContent": ["\"use client\"\n\nimport { AdminQuickNav } from './AdminQuickNav'\nimport { Badge } from '@/components/ui/badge'\nimport { Bell, Crown } from 'lucide-react'\n\ninterface AdminDashboardHeaderProps {\n  alertsCount?: number\n}\n\nexport function AdminDashboardHeader({ alertsCount = 0 }: AdminDashboardHeaderProps) {\n  return (\n    <div className=\"mb-8\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text flex items-center gap-2\">\n            <Crown className=\"h-8 w-8 text-yellow-500\" />\n            لوحة تحكم الإدارة\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n            إدارة شاملة للمنصة والمستخدمين والطلبات\n          </p>\n          {alertsCount > 0 && (\n            <div className=\"flex items-center gap-2 mt-2\">\n              <Bell className=\"h-4 w-4 text-amber-500\" />\n              <Badge variant=\"destructive\" className=\"text-xs\">\n                {alertsCount} تنبيه جديد\n              </Badge>\n            </div>\n          )}\n        </div>\n        \n        <AdminQuickNav />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAUO,SAAS,qBAAqB,EAAE,cAAc,CAAC,EAA6B;IACjF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;sCAG/C,6LAAC;4BAAE,WAAU;sCAAoD;;;;;;wBAGhE,cAAc,mBACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;;wCACpC;wCAAY;;;;;;;;;;;;;;;;;;;8BAMrB,6LAAC,+IAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;AAItB;KA1BgB", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,qKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV;KArBS", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qXACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AIModelCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { AIModel } from '@/types/ai-models'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Switch } from '@/components/ui/switch'\nimport { \n  Brain, \n  Settings, \n  TestTube, \n  Activity, \n  DollarSign, \n  Clock, \n  CheckCircle, \n  XCircle, \n  AlertTriangle,\n  MoreHorizontal,\n  Edit,\n  Trash2,\n  BarChart3,\n  Zap\n} from 'lucide-react'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from '@/components/ui/tooltip'\n\ninterface AIModelCardProps {\n  model: AIModel\n  onEdit?: (model: AIModel) => void\n  onDelete?: (model: AIModel) => void\n  onTest?: (model: AIModel) => void\n  onToggleActive?: (model: AIModel, isActive: boolean) => void\n  onViewUsage?: (model: AIModel) => void\n  onSettings?: (model: AIModel) => void\n}\n\nexport function AIModelCard({\n  model,\n  onEdit,\n  onDelete,\n  onTest,\n  onToggleActive,\n  onViewUsage,\n  onSettings\n}: AIModelCardProps) {\n  const [isLoading, setIsLoading] = useState(false)\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active': return 'bg-green-500'\n      case 'inactive': return 'bg-gray-500'\n      case 'error': return 'bg-red-500'\n      case 'testing': return 'bg-yellow-500'\n      default: return 'bg-gray-500'\n    }\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'active': return <CheckCircle className=\"h-4 w-4\" />\n      case 'inactive': return <XCircle className=\"h-4 w-4\" />\n      case 'error': return <AlertTriangle className=\"h-4 w-4\" />\n      case 'testing': return <TestTube className=\"h-4 w-4\" />\n      default: return <XCircle className=\"h-4 w-4\" />\n    }\n  }\n\n  const getProviderColor = (provider: string) => {\n    switch (provider) {\n      case 'openai': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n      case 'anthropic': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'\n      case 'google': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'\n      case 'meta': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'\n      case 'stability': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300'\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n    }\n  }\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'text': return '📝'\n      case 'image': return '🖼️'\n      case 'audio': return '🎵'\n      case 'multimodal': return '🔄'\n      case 'code': return '💻'\n      case 'embedding': return '🔗'\n      default: return '🤖'\n    }\n  }\n\n  const handleToggleActive = async (checked: boolean) => {\n    setIsLoading(true)\n    try {\n      await onToggleActive?.(model, checked)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleTest = async () => {\n    setIsLoading(true)\n    try {\n      await onTest?.(model)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('ar-MA', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2\n    }).format(amount)\n  }\n\n  const formatNumber = (num: number) => {\n    return new Intl.NumberFormat('ar-MA').format(num)\n  }\n\n  return (\n    <Card className=\"relative overflow-hidden\">\n      {/* شريط الحالة */}\n      <div className={`absolute top-0 left-0 right-0 h-1 ${getStatusColor(model.status)}`} />\n      \n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"text-2xl\">{model.icon || getTypeIcon(model.type)}</div>\n            <div>\n              <CardTitle className=\"text-lg arabic-text\">{model.name}</CardTitle>\n              <div className=\"flex items-center gap-2 mt-1\">\n                <Badge variant=\"secondary\" className={getProviderColor(model.provider)}>\n                  {model.provider}\n                </Badge>\n                <Badge variant=\"outline\">\n                  {getTypeIcon(model.type)} {model.type}\n                </Badge>\n                <div className=\"flex items-center gap-1\">\n                  {getStatusIcon(model.status)}\n                  <span className=\"text-sm text-muted-foreground\">{model.status}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <div className=\"flex items-center gap-2\">\n                    <Switch\n                      checked={model.isActive}\n                      onCheckedChange={handleToggleActive}\n                      disabled={isLoading}\n                    />\n                  </div>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>{model.isActive ? 'إلغاء التفعيل' : 'تفعيل'}</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" size=\"sm\">\n                  <MoreHorizontal className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <DropdownMenuItem onClick={() => onEdit?.(model)}>\n                  <Edit className=\"h-4 w-4 mr-2\" />\n                  تحرير\n                </DropdownMenuItem>\n                <DropdownMenuItem onClick={() => onSettings?.(model)}>\n                  <Settings className=\"h-4 w-4 mr-2\" />\n                  الإعدادات\n                </DropdownMenuItem>\n                <DropdownMenuItem onClick={handleTest} disabled={!model.isActive || isLoading}>\n                  <TestTube className=\"h-4 w-4 mr-2\" />\n                  اختبار\n                </DropdownMenuItem>\n                <DropdownMenuItem onClick={() => onViewUsage?.(model)}>\n                  <BarChart3 className=\"h-4 w-4 mr-2\" />\n                  الإحصائيات\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem \n                  onClick={() => onDelete?.(model)}\n                  className=\"text-destructive\"\n                >\n                  <Trash2 className=\"h-4 w-4 mr-2\" />\n                  حذف\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n        \n        {model.description && (\n          <p className=\"text-sm text-muted-foreground arabic-text mt-2\">\n            {model.description}\n          </p>\n        )}\n\n        {/* Base URL */}\n        {model.baseUrl && (\n          <div className=\"mt-2\">\n            <p className=\"text-xs text-muted-foreground\">\n              <span className=\"font-medium\">Base URL:</span> {model.baseUrl}\n            </p>\n          </div>\n        )}\n      </CardHeader>\n\n      <CardContent className=\"space-y-4\">\n        {/* النماذج المحددة */}\n        {model.selectedModels && model.selectedModels.length > 0 && (\n          <div>\n            <h4 className=\"text-sm font-medium arabic-text mb-2\">النماذج المحددة</h4>\n            <div className=\"flex flex-wrap gap-1\">\n              {model.selectedModels.slice(0, 3).map((modelName) => (\n                <Badge\n                  key={modelName}\n                  variant=\"default\"\n                  className=\"text-xs\"\n                >\n                  {modelName}\n                </Badge>\n              ))}\n              {model.selectedModels.length > 3 && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  +{model.selectedModels.length - 3}\n                </Badge>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* النماذج الفرعية */}\n        {model.subModels.length > 0 && (\n          <div>\n            <h4 className=\"text-sm font-medium arabic-text mb-2\">النماذج الفرعية النشطة</h4>\n            <div className=\"flex flex-wrap gap-1\">\n              {model.subModels.slice(0, 3).map((subModel) => (\n                <Badge\n                  key={subModel.id}\n                  variant={subModel.isActive ? \"default\" : \"secondary\"}\n                  className=\"text-xs\"\n                >\n                  {subModel.name}\n                  {subModel.isDefault && <span className=\"mr-1\">⭐</span>}\n                </Badge>\n              ))}\n              {model.subModels.length > 3 && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  +{model.subModels.length - 3}\n                </Badge>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* إحصائيات الاستخدام */}\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Activity className=\"h-4 w-4 text-blue-500\" />\n              <span className=\"text-sm text-muted-foreground\">الطلبات</span>\n            </div>\n            <p className=\"text-lg font-semibold\">\n              {formatNumber(model.usage.totalRequests)}\n            </p>\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <DollarSign className=\"h-4 w-4 text-green-500\" />\n              <span className=\"text-sm text-muted-foreground\">التكلفة</span>\n            </div>\n            <p className=\"text-lg font-semibold\">\n              {formatCurrency(model.usage.totalCost)}\n            </p>\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Clock className=\"h-4 w-4 text-orange-500\" />\n              <span className=\"text-sm text-muted-foreground\">متوسط الاستجابة</span>\n            </div>\n            <p className=\"text-lg font-semibold\">\n              {model.usage.averageResponseTime}ms\n            </p>\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Zap className=\"h-4 w-4 text-purple-500\" />\n              <span className=\"text-sm text-muted-foreground\">معدل النجاح</span>\n            </div>\n            <p className=\"text-lg font-semibold\">\n              {model.usage.successRate}%\n            </p>\n          </div>\n        </div>\n\n        {/* آخر اختبار */}\n        {model.lastTestedAt && model.testResult && (\n          <div className=\"border-t pt-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-muted-foreground\">آخر اختبار</span>\n              <div className=\"flex items-center gap-2\">\n                {model.testResult.success ? (\n                  <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                ) : (\n                  <XCircle className=\"h-4 w-4 text-red-500\" />\n                )}\n                <span className=\"text-sm\">\n                  {model.testResult.responseTime}ms\n                </span>\n              </div>\n            </div>\n            <p className=\"text-xs text-muted-foreground mt-1\">\n              {new Date(model.lastTestedAt).toLocaleDateString('ar-MA')}\n            </p>\n          </div>\n        )}\n\n        {/* أزرار الإجراءات */}\n        <div className=\"flex gap-2 pt-2\">\n          <Button \n            size=\"sm\" \n            variant=\"outline\" \n            onClick={handleTest}\n            disabled={!model.isActive || isLoading}\n            className=\"flex-1\"\n          >\n            <TestTube className=\"h-4 w-4 mr-2\" />\n            اختبار\n          </Button>\n          <Button \n            size=\"sm\" \n            variant=\"outline\" \n            onClick={() => onViewUsage?.(model)}\n            className=\"flex-1\"\n          >\n            <BarChart3 className=\"h-4 w-4 mr-2\" />\n            الإحصائيات\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAOA;;;AA/BA;;;;;;;;;AAgDO,SAAS,YAAY,EAC1B,KAAK,EACL,MAAM,EACN,QAAQ,EACR,MAAM,EACN,cAAc,EACd,WAAW,EACX,UAAU,EACO;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAY,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAS,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAC9C,KAAK;gBAAW,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC3C;gBAAS,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QACrC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,aAAa;QACb,IAAI;YACF,MAAM,iBAAiB,OAAO;QAChC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;QACb,IAAI;YACF,MAAM,SAAS;QACjB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAC/C;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BAEd,6LAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,eAAe,MAAM,MAAM,GAAG;;;;;;0BAEnF,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAY,MAAM,IAAI,IAAI,YAAY,MAAM,IAAI;;;;;;kDAC/D,6LAAC;;0DACC,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAuB,MAAM,IAAI;;;;;;0DACtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAW,iBAAiB,MAAM,QAAQ;kEAClE,MAAM,QAAQ;;;;;;kEAEjB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;;4DACZ,YAAY,MAAM,IAAI;4DAAE;4DAAE,MAAM,IAAI;;;;;;;kEAEvC,6LAAC;wDAAI,WAAU;;4DACZ,cAAc,MAAM,MAAM;0EAC3B,6LAAC;gEAAK,WAAU;0EAAiC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMrE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,sIAAA,CAAA,kBAAe;kDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAS,MAAM,QAAQ;4DACvB,iBAAiB;4DACjB,UAAU;;;;;;;;;;;;;;;;8DAIhB,6LAAC,sIAAA,CAAA,iBAAc;8DACb,cAAA,6LAAC;kEAAG,MAAM,QAAQ,GAAG,kBAAkB;;;;;;;;;;;;;;;;;;;;;;kDAK7C,6LAAC,+IAAA,CAAA,eAAY;;0DACX,6LAAC,+IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;8DAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gDAAC,OAAM;;kEACzB,6LAAC,+IAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,SAAS;;0EACxC,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,6LAAC,+IAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,aAAa;;0EAC5C,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,6LAAC,+IAAA,CAAA,mBAAgB;wDAAC,SAAS;wDAAY,UAAU,CAAC,MAAM,QAAQ,IAAI;;0EAClE,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,6LAAC,+IAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,cAAc;;0EAC7C,6LAAC,qNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGxC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kEACtB,6LAAC,+IAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,WAAW;wDAC1B,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ5C,MAAM,WAAW,kBAChB,6LAAC;wBAAE,WAAU;kCACV,MAAM,WAAW;;;;;;oBAKrB,MAAM,OAAO,kBACZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;8CACX,6LAAC;oCAAK,WAAU;8CAAc;;;;;;gCAAgB;gCAAE,MAAM,OAAO;;;;;;;;;;;;;;;;;;0BAMrE,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,MAAM,cAAc,IAAI,MAAM,cAAc,CAAC,MAAM,GAAG,mBACrD,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,0BACrC,6LAAC,oIAAA,CAAA,QAAK;4CAEJ,SAAQ;4CACR,WAAU;sDAET;2CAJI;;;;;oCAOR,MAAM,cAAc,CAAC,MAAM,GAAG,mBAC7B,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAU;4CACzC,MAAM,cAAc,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;oBAQzC,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAChC,6LAAC,oIAAA,CAAA,QAAK;4CAEJ,SAAS,SAAS,QAAQ,GAAG,YAAY;4CACzC,WAAU;;gDAET,SAAS,IAAI;gDACb,SAAS,SAAS,kBAAI,6LAAC;oDAAK,WAAU;8DAAO;;;;;;;2CALzC,SAAS,EAAE;;;;;oCAQnB,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAU;4CACzC,MAAM,SAAS,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;kCAQrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,6LAAC;wCAAE,WAAU;kDACV,aAAa,MAAM,KAAK,CAAC,aAAa;;;;;;;;;;;;0CAI3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,6LAAC;wCAAE,WAAU;kDACV,eAAe,MAAM,KAAK,CAAC,SAAS;;;;;;;;;;;;0CAIzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,6LAAC;wCAAE,WAAU;;4CACV,MAAM,KAAK,CAAC,mBAAmB;4CAAC;;;;;;;;;;;;;0CAIrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,6LAAC;wCAAE,WAAU;;4CACV,MAAM,KAAK,CAAC,WAAW;4CAAC;;;;;;;;;;;;;;;;;;;oBAM9B,MAAM,YAAY,IAAI,MAAM,UAAU,kBACrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;kDAChD,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,UAAU,CAAC,OAAO,iBACvB,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DAErB,6LAAC;gDAAK,WAAU;;oDACb,MAAM,UAAU,CAAC,YAAY;oDAAC;;;;;;;;;;;;;;;;;;;0CAIrC,6LAAC;gCAAE,WAAU;0CACV,IAAI,KAAK,MAAM,YAAY,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kCAMvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,CAAC,MAAM,QAAQ,IAAI;gCAC7B,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS,IAAM,cAAc;gCAC7B,WAAU;;kDAEV,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD;GA9TgB;KAAA", "debugId": null}}, {"offset": {"line": 1696, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 1761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Slider({\n  className,\n  defaultValue,\n  value,\n  min = 0,\n  max = 100,\n  ...props\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\n  const _values = React.useMemo(\n    () =>\n      Array.isArray(value)\n        ? value\n        : Array.isArray(defaultValue)\n          ? defaultValue\n          : [min, max],\n    [value, defaultValue, min, max]\n  )\n\n  return (\n    <SliderPrimitive.Root\n      data-slot=\"slider\"\n      defaultValue={defaultValue}\n      value={value}\n      min={min}\n      max={max}\n      className={cn(\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    >\n      <SliderPrimitive.Track\n        data-slot=\"slider-track\"\n        className={cn(\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\n        )}\n      >\n        <SliderPrimitive.Range\n          data-slot=\"slider-range\"\n          className={cn(\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\n          )}\n        />\n      </SliderPrimitive.Track>\n      {Array.from({ length: _values.length }, (_, index) => (\n        <SliderPrimitive.Thumb\n          data-slot=\"slider-thumb\"\n          key={index}\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\n        />\n      ))}\n    </SliderPrimitive.Root>\n  )\n}\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;;IAClD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;mCAC1B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;gBAAC;gBAAK;aAAI;kCAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,6LAAC,qKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf;GArDS;KAAA", "debugId": null}}, {"offset": {"line": 1843, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 2092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AIModelForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { AIModel, AIProvider, ModelType, CreateModelRequest, UpdateModelRequest } from '@/types/ai-models'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Switch } from '@/components/ui/switch'\nimport { Slider } from '@/components/ui/slider'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Tabs,\n  TabsContent,\n  TabsList,\n  TabsTrigger,\n} from '@/components/ui/tabs'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Eye, EyeOff, TestTube, Save, X } from 'lucide-react'\n\ninterface AIModelFormProps {\n  model?: AIModel\n  isOpen: boolean\n  onClose: () => void\n  onSave: (data: CreateModelRequest | UpdateModelRequest) => Promise<void>\n  onTest?: (data: any) => Promise<void>\n}\n\nconst PROVIDERS: {\n  value: AIProvider;\n  label: string;\n  description: string;\n  baseUrl: string;\n  models: string[];\n}[] = [\n  {\n    value: 'openai',\n    label: 'OpenAI',\n    description: 'GPT, DALL-E, Whisper',\n    baseUrl: 'https://api.openai.com/v1',\n    models: ['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo', 'dall-e-3', 'dall-e-2', 'whisper-1', 'tts-1', 'text-embedding-ada-002']\n  },\n  {\n    value: 'anthropic',\n    label: 'Anthropic',\n    description: 'Claude Models',\n    baseUrl: 'https://api.anthropic.com',\n    models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-2.1', 'claude-2.0', 'claude-instant-1.2']\n  },\n  {\n    value: 'google',\n    label: 'Google',\n    description: 'Gemini, PaLM',\n    baseUrl: 'https://generativelanguage.googleapis.com/v1',\n    models: ['gemini-pro', 'gemini-pro-vision', 'gemini-1.5-pro', 'gemini-1.5-flash', 'text-bison-001', 'chat-bison-001']\n  },\n  {\n    value: 'meta',\n    label: 'Meta',\n    description: 'LLaMA Models',\n    baseUrl: 'https://api.llama-api.com/v1',\n    models: ['llama-2-70b-chat', 'llama-2-13b-chat', 'llama-2-7b-chat', 'code-llama-34b', 'code-llama-13b', 'code-llama-7b']\n  },\n  {\n    value: 'stability',\n    label: 'Stability AI',\n    description: 'Stable Diffusion',\n    baseUrl: 'https://api.stability.ai/v1',\n    models: ['stable-diffusion-xl-1024-v1-0', 'stable-diffusion-v1-6', 'stable-diffusion-512-v2-1', 'stable-diffusion-xl-beta-v2-2-2']\n  },\n  {\n    value: 'cohere',\n    label: 'Cohere',\n    description: 'Command Models',\n    baseUrl: 'https://api.cohere.ai/v1',\n    models: ['command', 'command-light', 'command-nightly', 'command-r', 'command-r-plus', 'embed-english-v3.0', 'embed-multilingual-v3.0']\n  },\n  {\n    value: 'huggingface',\n    label: 'Hugging Face',\n    description: 'Open Source Models',\n    baseUrl: 'https://api-inference.huggingface.co',\n    models: ['microsoft/DialoGPT-large', 'facebook/blenderbot-400M-distill', 'microsoft/DialoGPT-medium', 'facebook/blenderbot-1B-distill']\n  },\n  {\n    value: 'deepseek',\n    label: 'DeepSeek',\n    description: 'DeepSeek Models',\n    baseUrl: 'https://api.deepseek.com/v1',\n    models: ['deepseek-chat', 'deepseek-coder', 'deepseek-math', 'deepseek-67b-chat', 'deepseek-7b-chat']\n  }\n]\n\nconst MODEL_TYPES: { value: ModelType; label: string; icon: string }[] = [\n  { value: 'text', label: 'نص', icon: '📝' },\n  { value: 'image', label: 'صورة', icon: '🖼️' },\n  { value: 'audio', label: 'صوت', icon: '🎵' },\n  { value: 'multimodal', label: 'متعدد الوسائط', icon: '🔄' },\n  { value: 'code', label: 'كود', icon: '💻' },\n  { value: 'embedding', label: 'تضمين', icon: '🔗' }\n]\n\nexport function AIModelForm({ model, isOpen, onClose, onSave, onTest }: AIModelFormProps) {\n  const [formData, setFormData] = useState<any>({\n    provider: 'openai' as AIProvider,\n    description: '',\n    apiKey: '',\n    baseUrl: '',\n    isActive: true,\n    settings: {\n      temperature: 0.7,\n      maxTokens: 2048,\n      topP: 1,\n      frequencyPenalty: 0,\n      presencePenalty: 0,\n      systemPrompt: ''\n    }\n  })\n\n  const [showApiKey, setShowApiKey] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [testPrompt, setTestPrompt] = useState('مرحباً، كيف يمكنني مساعدتك؟')\n  const [selectedModels, setSelectedModels] = useState<string[]>([])\n  const [availableModels, setAvailableModels] = useState<string[]>([])\n  const [healthCheckLoading, setHealthCheckLoading] = useState(false)\n  const [healthCheckResult, setHealthCheckResult] = useState<any>(null)\n\n  useEffect(() => {\n    if (model) {\n      setFormData({\n        provider: model.provider,\n        description: model.description || '',\n        apiKey: model.apiKey || '',\n        baseUrl: model.baseUrl || '',\n        isActive: model.isActive,\n        settings: { ...model.settings }\n      })\n      // تحديد النماذج المحددة من النماذج الفرعية الموجودة\n      setSelectedModels(model.selectedModels || model.subModels?.map(sm => sm.name) || [])\n    } else {\n      // إعادة تعيين النموذج للإضافة الجديدة\n      const defaultProvider = PROVIDERS[0]\n      setFormData({\n        provider: 'openai' as AIProvider,\n        description: '',\n        apiKey: '',\n        baseUrl: defaultProvider.baseUrl,\n        isActive: true,\n        settings: {\n          temperature: 0.7,\n          maxTokens: 2048,\n          topP: 1,\n          frequencyPenalty: 0,\n          presencePenalty: 0,\n          systemPrompt: ''\n        }\n      })\n      setSelectedModels([])\n      setAvailableModels(defaultProvider.models)\n    }\n  }, [model, isOpen])\n\n  // تحديث النماذج المتاحة عند تغيير مقدم الخدمة\n  useEffect(() => {\n    const provider = PROVIDERS.find(p => p.value === formData.provider)\n    if (provider) {\n      setAvailableModels(provider.models)\n      setFormData(prev => ({\n        ...prev,\n        baseUrl: provider.baseUrl\n      }))\n      // إعادة تعيين النماذج المحددة عند تغيير المقدم\n      setSelectedModels([])\n    }\n  }, [formData.provider])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    // التحقق من تحديد نماذج\n    if (selectedModels.length === 0) {\n      alert('يرجى تحديد نموذج واحد على الأقل')\n      return\n    }\n\n    setIsLoading(true)\n\n    try {\n      // توليد اسم النموذج تلقائياً من مقدم الخدمة\n      const provider = PROVIDERS.find(p => p.value === formData.provider)\n      const generatedName = provider ? provider.label : formData.provider\n\n      // تحديد نوع النموذج تلقائياً بناءً على النماذج المحددة\n      const modelType = determineModelType(selectedModels)\n\n      // إنشاء النماذج الفرعية من النماذج المحددة\n      const subModels = selectedModels.map((modelName, index) => ({\n        id: `${formData.provider}-${modelName}-${Date.now()}-${index}`,\n        name: modelName,\n        modelId: model?.id || `new-model-${Date.now()}`,\n        description: `نموذج ${modelName} من ${formData.provider}`,\n        version: '1.0',\n        capabilities: getModelCapabilities(modelName),\n        pricing: getModelPricing(formData.provider, modelName),\n        limits: getModelLimits(modelName),\n        isActive: true,\n        isDefault: index === 0, // النموذج الأول يكون افتراضي\n        tags: [formData.provider, modelType],\n        releaseDate: new Date().toISOString()\n      }))\n\n      const dataToSave = {\n        ...formData,\n        name: generatedName,\n        type: modelType,\n        subModels,\n        selectedModels\n      }\n\n      await onSave(dataToSave)\n      onClose()\n    } catch (error) {\n      console.error('Error saving model:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // دالة لتحديد نوع النموذج تلقائياً\n  const determineModelType = (models: string[]): ModelType => {\n    // فحص النماذج لتحديد النوع الأساسي\n    const hasImageModel = models.some(m =>\n      m.includes('dall-e') || m.includes('stable-diffusion') || m.includes('vision')\n    )\n    const hasAudioModel = models.some(m =>\n      m.includes('whisper') || m.includes('tts')\n    )\n    const hasEmbeddingModel = models.some(m =>\n      m.includes('embed')\n    )\n    const hasCodeModel = models.some(m =>\n      m.includes('code') || m.includes('coder')\n    )\n    const hasMultimodalModel = models.some(m =>\n      m.includes('vision') || m.includes('multimodal')\n    )\n\n    if (hasMultimodalModel) return 'multimodal'\n    if (hasImageModel) return 'image'\n    if (hasAudioModel) return 'audio'\n    if (hasEmbeddingModel) return 'embedding'\n    if (hasCodeModel) return 'code'\n\n    return 'text' // افتراضي\n  }\n\n  // دالة مساعدة لتحديد قدرات النموذج\n  const getModelCapabilities = (modelName: string): string[] => {\n    const capabilities = ['text-generation']\n\n    if (modelName.includes('gpt-4') || modelName.includes('claude-3')) {\n      capabilities.push('reasoning', 'analysis', 'code')\n    }\n    if (modelName.includes('vision') || modelName.includes('dall-e')) {\n      capabilities.push('image-understanding', 'image-generation')\n    }\n    if (modelName.includes('whisper') || modelName.includes('tts')) {\n      capabilities.push('audio-processing')\n    }\n    if (modelName.includes('embed')) {\n      capabilities.push('embeddings')\n    }\n    if (modelName.includes('code') || modelName.includes('coder')) {\n      capabilities.push('code-generation', 'code-analysis')\n    }\n\n    return capabilities\n  }\n\n  // دالة مساعدة لتحديد التسعير\n  const getModelPricing = (provider: string, modelName: string) => {\n    const pricingMap: Record<string, { input: number, output: number }> = {\n      'gpt-4-turbo': { input: 0.01, output: 0.03 },\n      'gpt-4': { input: 0.03, output: 0.06 },\n      'gpt-3.5-turbo': { input: 0.0005, output: 0.0015 },\n      'claude-3-opus': { input: 0.015, output: 0.075 },\n      'claude-3-sonnet': { input: 0.003, output: 0.015 },\n      'claude-3-haiku': { input: 0.00025, output: 0.00125 },\n      'gemini-pro': { input: 0.0005, output: 0.0015 },\n      'default': { input: 0.001, output: 0.002 }\n    }\n\n    const pricing = pricingMap[modelName] || pricingMap['default']\n    return {\n      inputTokens: pricing.input,\n      outputTokens: pricing.output,\n      currency: 'USD',\n      unit: '1K tokens'\n    }\n  }\n\n  // دالة مساعدة لتحديد حدود النموذج\n  const getModelLimits = (modelName: string) => {\n    const limitsMap: Record<string, any> = {\n      'gpt-4-turbo': { maxTokens: 128000, requestsPerMinute: 500, requestsPerDay: 10000, contextWindow: 128000 },\n      'gpt-4': { maxTokens: 8192, requestsPerMinute: 200, requestsPerDay: 5000, contextWindow: 8192 },\n      'gpt-3.5-turbo': { maxTokens: 16385, requestsPerMinute: 3500, requestsPerDay: 50000, contextWindow: 16385 },\n      'claude-3-opus': { maxTokens: 200000, requestsPerMinute: 50, requestsPerDay: 1000, contextWindow: 200000 },\n      'default': { maxTokens: 4096, requestsPerMinute: 60, requestsPerDay: 1000, contextWindow: 4096 }\n    }\n\n    return limitsMap[modelName] || limitsMap['default']\n  }\n\n  const handleTest = async () => {\n    if (!onTest) return\n    \n    setIsLoading(true)\n    try {\n      await onTest({\n        modelId: model?.id,\n        prompt: testPrompt,\n        settings: formData.settings\n      })\n    } catch (error) {\n      console.error('Error testing model:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const updateFormData = (field: string, value: any) => {\n    setFormData((prev: any) => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  const updateSettings = (field: string, value: any) => {\n    setFormData((prev: any) => ({\n      ...prev,\n      settings: {\n        ...prev.settings,\n        [field]: value\n      }\n    }))\n  }\n\n  const handleModelToggle = (modelName: string) => {\n    setSelectedModels(prev => {\n      if (prev.includes(modelName)) {\n        return prev.filter(m => m !== modelName)\n      } else {\n        return [...prev, modelName]\n      }\n    })\n  }\n\n  const handleSelectAllModels = () => {\n    if (selectedModels.length === availableModels.length) {\n      setSelectedModels([])\n    } else {\n      setSelectedModels([...availableModels])\n    }\n  }\n\n  const handleHealthCheck = async () => {\n    if (!formData.baseUrl || selectedModels.length === 0) {\n      alert('يرجى إدخال Base URL وتحديد نماذج للفحص')\n      return\n    }\n\n    setHealthCheckLoading(true)\n    setHealthCheckResult(null)\n\n    try {\n      const response = await fetch('/api/ai-models/health-check', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          provider: formData.provider,\n          baseUrl: formData.baseUrl,\n          apiKey: formData.apiKey,\n          selectedModels: selectedModels\n        }),\n      })\n\n      const result = await response.json()\n\n      if (response.ok) {\n        setHealthCheckResult(result)\n      } else {\n        setHealthCheckResult({\n          status: 'error',\n          error: result.error || 'فشل في فحص الصحة'\n        })\n      }\n    } catch (error) {\n      setHealthCheckResult({\n        status: 'error',\n        error: 'خطأ في الاتصال بالخادم'\n      })\n    } finally {\n      setHealthCheckLoading(false)\n    }\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"arabic-text\">\n            {model ? 'تحرير إعدادات مقدم الخدمة' : 'إضافة مقدم خدمة جديد'}\n          </DialogTitle>\n          <DialogDescription>\n            {model\n              ? 'قم بتحديث إعدادات مقدم الخدمة والنماذج المحددة'\n              : 'اختر مقدم خدمة وحدد النماذج المطلوبة للعمل بها'\n            }\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit}>\n          <Tabs defaultValue=\"basic\" className=\"w-full\">\n            <TabsList className=\"grid w-full grid-cols-3\">\n              <TabsTrigger value=\"basic\">المعلومات الأساسية</TabsTrigger>\n              <TabsTrigger value=\"settings\">الإعدادات</TabsTrigger>\n              <TabsTrigger value=\"test\">الاختبار</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"basic\" className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"provider\">مقدم الخدمة *</Label>\n                  <Select\n                    value={formData.provider}\n                    onValueChange={(value) => updateFormData('provider', value)}\n                  >\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {PROVIDERS.map((provider) => (\n                        <SelectItem key={provider.value} value={provider.value}>\n                          <div className=\"flex flex-col\">\n                            <span>{provider.label}</span>\n                            <span className=\"text-xs text-muted-foreground\">\n                              {provider.description}\n                            </span>\n                          </div>\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"isActive\" className=\"flex items-center gap-2\">\n                    <Switch\n                      id=\"isActive\"\n                      checked={formData.isActive}\n                      onCheckedChange={(checked) => updateFormData('isActive', checked)}\n                    />\n                    نشط\n                  </Label>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\">الوصف</Label>\n                <Textarea\n                  id=\"description\"\n                  value={formData.description}\n                  onChange={(e) => updateFormData('description', e.target.value)}\n                  placeholder=\"وصف مختصر للنموذج وقدراته\"\n                  rows={3}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"apiKey\">مفتاح API</Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"apiKey\"\n                    type={showApiKey ? 'text' : 'password'}\n                    value={formData.apiKey}\n                    onChange={(e) => updateFormData('apiKey', e.target.value)}\n                    placeholder=\"أدخل مفتاح API\"\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"absolute left-2 top-1/2 -translate-y-1/2\"\n                    onClick={() => setShowApiKey(!showApiKey)}\n                  >\n                    {showApiKey ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                  </Button>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"baseUrl\">Base URL *</Label>\n                <Input\n                  id=\"baseUrl\"\n                  value={formData.baseUrl}\n                  onChange={(e) => updateFormData('baseUrl', e.target.value)}\n                  placeholder=\"https://api.example.com/v1\"\n                  required\n                />\n                <p className=\"text-xs text-muted-foreground\">\n                  رابط API الأساسي لمقدم الخدمة\n                </p>\n              </div>\n\n              {/* اختيار النماذج المتاحة */}\n              {availableModels.length > 0 && (\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <Label>النماذج المتاحة</Label>\n                    <Button\n                      type=\"button\"\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={handleSelectAllModels}\n                    >\n                      {selectedModels.length === availableModels.length ? 'إلغاء تحديد الكل' : 'تحديد الكل'}\n                    </Button>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto border rounded-lg p-3\">\n                    {availableModels.map((modelName) => (\n                      <div key={modelName} className=\"flex items-center space-x-2 space-x-reverse\">\n                        <input\n                          type=\"checkbox\"\n                          id={`model-${modelName}`}\n                          checked={selectedModels.includes(modelName)}\n                          onChange={() => handleModelToggle(modelName)}\n                          className=\"rounded border-gray-300\"\n                        />\n                        <Label\n                          htmlFor={`model-${modelName}`}\n                          className=\"text-sm font-normal cursor-pointer flex-1\"\n                        >\n                          {modelName}\n                        </Label>\n                      </div>\n                    ))}\n                  </div>\n\n                  <p className=\"text-xs text-muted-foreground\">\n                    تم تحديد {selectedModels.length} من {availableModels.length} نموذج\n                  </p>\n                </div>\n              )}\n            </TabsContent>\n\n            <TabsContent value=\"settings\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">إعدادات التوليد</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div className=\"space-y-2\">\n                    <Label>درجة الحرارة: {formData.settings.temperature}</Label>\n                    <Slider\n                      value={[formData.settings.temperature]}\n                      onValueChange={([value]) => updateSettings('temperature', value)}\n                      max={2}\n                      min={0}\n                      step={0.1}\n                      className=\"w-full\"\n                    />\n                    <p className=\"text-xs text-muted-foreground\">\n                      قيم أعلى تعني إجابات أكثر إبداعاً وعشوائية\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label>الحد الأقصى للرموز: {formData.settings.maxTokens}</Label>\n                    <Slider\n                      value={[formData.settings.maxTokens]}\n                      onValueChange={([value]) => updateSettings('maxTokens', value)}\n                      max={8192}\n                      min={1}\n                      step={1}\n                      className=\"w-full\"\n                    />\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label>Top P: {formData.settings.topP}</Label>\n                    <Slider\n                      value={[formData.settings.topP]}\n                      onValueChange={([value]) => updateSettings('topP', value)}\n                      max={1}\n                      min={0}\n                      step={0.01}\n                      className=\"w-full\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label>عقوبة التكرار: {formData.settings.frequencyPenalty}</Label>\n                      <Slider\n                        value={[formData.settings.frequencyPenalty]}\n                        onValueChange={([value]) => updateSettings('frequencyPenalty', value)}\n                        max={2}\n                        min={0}\n                        step={0.1}\n                        className=\"w-full\"\n                      />\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label>عقوبة الحضور: {formData.settings.presencePenalty}</Label>\n                      <Slider\n                        value={[formData.settings.presencePenalty]}\n                        onValueChange={([value]) => updateSettings('presencePenalty', value)}\n                        max={2}\n                        min={0}\n                        step={0.1}\n                        className=\"w-full\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"systemPrompt\">الرسالة النظامية</Label>\n                    <Textarea\n                      id=\"systemPrompt\"\n                      value={formData.settings.systemPrompt}\n                      onChange={(e) => updateSettings('systemPrompt', e.target.value)}\n                      placeholder=\"أنت مساعد ذكي مفيد ومهذب...\"\n                      rows={4}\n                    />\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"test\" className=\"space-y-4\">\n              {/* فحص صحة API */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg flex items-center gap-2\">\n                    <Activity className=\"h-5 w-5\" />\n                    فحص صحة API\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <p className=\"text-sm text-muted-foreground\">\n                      تحقق من حالة الاتصال بـ API ومدى توفر النماذج المحددة\n                    </p>\n                    <Button\n                      type=\"button\"\n                      variant=\"outline\"\n                      onClick={handleHealthCheck}\n                      disabled={healthCheckLoading || !formData.baseUrl || selectedModels.length === 0}\n                    >\n                      {healthCheckLoading ? (\n                        <>\n                          <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2\"></div>\n                          جاري الفحص...\n                        </>\n                      ) : (\n                        <>\n                          <Activity className=\"h-4 w-4 mr-2\" />\n                          فحص الاتصال\n                        </>\n                      )}\n                    </Button>\n                  </div>\n\n                  {healthCheckResult && (\n                    <div className={`p-4 rounded-lg border ${\n                      healthCheckResult.status === 'active' ? 'bg-green-50 border-green-200' :\n                      healthCheckResult.status === 'warning' ? 'bg-yellow-50 border-yellow-200' :\n                      'bg-red-50 border-red-200'\n                    }`}>\n                      <div className=\"flex items-center gap-2 mb-2\">\n                        {healthCheckResult.status === 'active' && <CheckCircle className=\"h-5 w-5 text-green-600\" />}\n                        {healthCheckResult.status === 'warning' && <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />}\n                        {healthCheckResult.status === 'error' && <XCircle className=\"h-5 w-5 text-red-600\" />}\n                        <span className={`font-semibold ${\n                          healthCheckResult.status === 'active' ? 'text-green-800' :\n                          healthCheckResult.status === 'warning' ? 'text-yellow-800' :\n                          'text-red-800'\n                        }`}>\n                          {healthCheckResult.status === 'active' ? 'API يعمل بشكل طبيعي' :\n                           healthCheckResult.status === 'warning' ? 'API يعمل مع تحذيرات' :\n                           'API لا يعمل'}\n                        </span>\n                      </div>\n\n                      {healthCheckResult.responseTime && (\n                        <p className=\"text-sm text-muted-foreground mb-2\">\n                          وقت الاستجابة: {healthCheckResult.responseTime}ms\n                        </p>\n                      )}\n\n                      {healthCheckResult.error && (\n                        <p className=\"text-sm text-red-600 mb-2\">\n                          خطأ: {healthCheckResult.error}\n                        </p>\n                      )}\n\n                      {healthCheckResult.models && healthCheckResult.models.length > 0 && (\n                        <div className=\"mt-3\">\n                          <h4 className=\"text-sm font-medium mb-2\">حالة النماذج:</h4>\n                          <div className=\"space-y-1\">\n                            {healthCheckResult.models.map((model: any, index: number) => (\n                              <div key={index} className=\"flex items-center justify-between text-sm\">\n                                <span>{model.name}</span>\n                                <div className=\"flex items-center gap-2\">\n                                  <span className={`px-2 py-1 rounded text-xs ${\n                                    model.status === 'healthy' ? 'bg-green-100 text-green-800' :\n                                    'bg-red-100 text-red-800'\n                                  }`}>\n                                    {model.status === 'healthy' ? 'متاح' : 'غير متاح'}\n                                  </span>\n                                  <span className=\"text-muted-foreground\">{model.responseTime}ms</span>\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              {model && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"text-lg\">اختبار النموذج</CardTitle>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"testPrompt\">النص التجريبي</Label>\n                      <Textarea\n                        id=\"testPrompt\"\n                        value={testPrompt}\n                        onChange={(e) => setTestPrompt(e.target.value)}\n                        placeholder=\"أدخل نص لاختبار النموذج\"\n                        rows={3}\n                      />\n                    </div>\n\n                    <Button \n                      type=\"button\" \n                      onClick={handleTest}\n                      disabled={isLoading || !testPrompt.trim()}\n                      className=\"w-full\"\n                    >\n                      <TestTube className=\"h-4 w-4 mr-2\" />\n                      {isLoading ? 'جاري الاختبار...' : 'اختبار النموذج'}\n                    </Button>\n\n                    {model.testResult && (\n                      <div className=\"mt-4 p-4 border rounded-lg\">\n                        <div className=\"flex items-center gap-2 mb-2\">\n                          <Badge variant={model.testResult.success ? \"default\" : \"destructive\"}>\n                            {model.testResult.success ? 'نجح' : 'فشل'}\n                          </Badge>\n                          <span className=\"text-sm text-muted-foreground\">\n                            {model.testResult.responseTime}ms\n                          </span>\n                        </div>\n                        {model.testResult.error && (\n                          <p className=\"text-sm text-destructive\">{model.testResult.error}</p>\n                        )}\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n              )}\n            </TabsContent>\n          </Tabs>\n\n          <DialogFooter className=\"mt-6\">\n            <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n              <X className=\"h-4 w-4 mr-2\" />\n              إلغاء\n            </Button>\n            <Button type=\"submit\" disabled={isLoading}>\n              <Save className=\"h-4 w-4 mr-2\" />\n              {isLoading ? 'جاري الحفظ...' : 'حفظ'}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAQA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAjCA;;;;;;;;;;;;;;AA2CA,MAAM,YAMA;IACJ;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;YAAe;YAAS;YAAiB;YAAY;YAAY;YAAa;YAAS;SAAyB;IAC3H;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;YAA0B;YAA4B;YAA2B;YAAc;YAAc;SAAqB;IAC7I;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;YAAc;YAAqB;YAAkB;YAAoB;YAAkB;SAAiB;IACvH;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;YAAoB;YAAoB;YAAmB;YAAkB;YAAkB;SAAgB;IAC1H;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;YAAiC;YAAyB;YAA6B;SAAkC;IACpI;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;YAAW;YAAiB;YAAmB;YAAa;YAAkB;YAAsB;SAA0B;IACzI;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;YAA4B;YAAoC;YAA6B;SAAiC;IACzI;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;YAAiB;YAAkB;YAAiB;YAAqB;SAAmB;IACvG;CACD;AAED,MAAM,cAAmE;IACvE;QAAE,OAAO;QAAQ,OAAO;QAAM,MAAM;IAAK;IACzC;QAAE,OAAO;QAAS,OAAO;QAAQ,MAAM;IAAM;IAC7C;QAAE,OAAO;QAAS,OAAO;QAAO,MAAM;IAAK;IAC3C;QAAE,OAAO;QAAc,OAAO;QAAiB,MAAM;IAAK;IAC1D;QAAE,OAAO;QAAQ,OAAO;QAAO,MAAM;IAAK;IAC1C;QAAE,OAAO;QAAa,OAAO;QAAS,MAAM;IAAK;CAClD;AAEM,SAAS,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAoB;;IACtF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;QAC5C,UAAU;QACV,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;YACR,aAAa;YACb,WAAW;YACX,MAAM;YACN,kBAAkB;YAClB,iBAAiB;YACjB,cAAc;QAChB;IACF;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,OAAO;gBACT,YAAY;oBACV,UAAU,MAAM,QAAQ;oBACxB,aAAa,MAAM,WAAW,IAAI;oBAClC,QAAQ,MAAM,MAAM,IAAI;oBACxB,SAAS,MAAM,OAAO,IAAI;oBAC1B,UAAU,MAAM,QAAQ;oBACxB,UAAU;wBAAE,GAAG,MAAM,QAAQ;oBAAC;gBAChC;gBACA,oDAAoD;gBACpD,kBAAkB,MAAM,cAAc,IAAI,MAAM,SAAS,EAAE;6CAAI,CAAA,KAAM,GAAG,IAAI;+CAAK,EAAE;YACrF,OAAO;gBACL,sCAAsC;gBACtC,MAAM,kBAAkB,SAAS,CAAC,EAAE;gBACpC,YAAY;oBACV,UAAU;oBACV,aAAa;oBACb,QAAQ;oBACR,SAAS,gBAAgB,OAAO;oBAChC,UAAU;oBACV,UAAU;wBACR,aAAa;wBACb,WAAW;wBACX,MAAM;wBACN,kBAAkB;wBAClB,iBAAiB;wBACjB,cAAc;oBAChB;gBACF;gBACA,kBAAkB,EAAE;gBACpB,mBAAmB,gBAAgB,MAAM;YAC3C;QACF;gCAAG;QAAC;QAAO;KAAO;IAElB,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW,UAAU,IAAI;kDAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,QAAQ;;YAClE,IAAI,UAAU;gBACZ,mBAAmB,SAAS,MAAM;gBAClC;6CAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,SAAS,SAAS,OAAO;wBAC3B,CAAC;;gBACD,+CAA+C;gBAC/C,kBAAkB,EAAE;YACtB;QACF;gCAAG;QAAC,SAAS,QAAQ;KAAC;IAEtB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,wBAAwB;QACxB,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,MAAM;YACN;QACF;QAEA,aAAa;QAEb,IAAI;YACF,4CAA4C;YAC5C,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,QAAQ;YAClE,MAAM,gBAAgB,WAAW,SAAS,KAAK,GAAG,SAAS,QAAQ;YAEnE,uDAAuD;YACvD,MAAM,YAAY,mBAAmB;YAErC,2CAA2C;YAC3C,MAAM,YAAY,eAAe,GAAG,CAAC,CAAC,WAAW,QAAU,CAAC;oBAC1D,IAAI,GAAG,SAAS,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,OAAO;oBAC9D,MAAM;oBACN,SAAS,OAAO,MAAM,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI;oBAC/C,aAAa,CAAC,MAAM,EAAE,UAAU,IAAI,EAAE,SAAS,QAAQ,EAAE;oBACzD,SAAS;oBACT,cAAc,qBAAqB;oBACnC,SAAS,gBAAgB,SAAS,QAAQ,EAAE;oBAC5C,QAAQ,eAAe;oBACvB,UAAU;oBACV,WAAW,UAAU;oBACrB,MAAM;wBAAC,SAAS,QAAQ;wBAAE;qBAAU;oBACpC,aAAa,IAAI,OAAO,WAAW;gBACrC,CAAC;YAED,MAAM,aAAa;gBACjB,GAAG,QAAQ;gBACX,MAAM;gBACN,MAAM;gBACN;gBACA;YACF;YAEA,MAAM,OAAO;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,aAAa;QACf;IACF;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,CAAC;QAC1B,mCAAmC;QACnC,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAChC,EAAE,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,uBAAuB,EAAE,QAAQ,CAAC;QAEvE,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAChC,EAAE,QAAQ,CAAC,cAAc,EAAE,QAAQ,CAAC;QAEtC,MAAM,oBAAoB,OAAO,IAAI,CAAC,CAAA,IACpC,EAAE,QAAQ,CAAC;QAEb,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,IAC/B,EAAE,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC;QAEnC,MAAM,qBAAqB,OAAO,IAAI,CAAC,CAAA,IACrC,EAAE,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC;QAGrC,IAAI,oBAAoB,OAAO;QAC/B,IAAI,eAAe,OAAO;QAC1B,IAAI,eAAe,OAAO;QAC1B,IAAI,mBAAmB,OAAO;QAC9B,IAAI,cAAc,OAAO;QAEzB,OAAO,OAAO,UAAU;;IAC1B;IAEA,mCAAmC;IACnC,MAAM,uBAAuB,CAAC;QAC5B,MAAM,eAAe;YAAC;SAAkB;QAExC,IAAI,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,aAAa;YACjE,aAAa,IAAI,CAAC,aAAa,YAAY;QAC7C;QACA,IAAI,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,WAAW;YAChE,aAAa,IAAI,CAAC,uBAAuB;QAC3C;QACA,IAAI,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,QAAQ;YAC9D,aAAa,IAAI,CAAC;QACpB;QACA,IAAI,UAAU,QAAQ,CAAC,UAAU;YAC/B,aAAa,IAAI,CAAC;QACpB;QACA,IAAI,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,UAAU;YAC7D,aAAa,IAAI,CAAC,mBAAmB;QACvC;QAEA,OAAO;IACT;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB,CAAC,UAAkB;QACzC,MAAM,aAAgE;YACpE,eAAe;gBAAE,OAAO;gBAAM,QAAQ;YAAK;YAC3C,SAAS;gBAAE,OAAO;gBAAM,QAAQ;YAAK;YACrC,iBAAiB;gBAAE,OAAO;gBAAQ,QAAQ;YAAO;YACjD,iBAAiB;gBAAE,OAAO;gBAAO,QAAQ;YAAM;YAC/C,mBAAmB;gBAAE,OAAO;gBAAO,QAAQ;YAAM;YACjD,kBAAkB;gBAAE,OAAO;gBAAS,QAAQ;YAAQ;YACpD,cAAc;gBAAE,OAAO;gBAAQ,QAAQ;YAAO;YAC9C,WAAW;gBAAE,OAAO;gBAAO,QAAQ;YAAM;QAC3C;QAEA,MAAM,UAAU,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,UAAU;QAC9D,OAAO;YACL,aAAa,QAAQ,KAAK;YAC1B,cAAc,QAAQ,MAAM;YAC5B,UAAU;YACV,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,MAAM,iBAAiB,CAAC;QACtB,MAAM,YAAiC;YACrC,eAAe;gBAAE,WAAW;gBAAQ,mBAAmB;gBAAK,gBAAgB;gBAAO,eAAe;YAAO;YACzG,SAAS;gBAAE,WAAW;gBAAM,mBAAmB;gBAAK,gBAAgB;gBAAM,eAAe;YAAK;YAC9F,iBAAiB;gBAAE,WAAW;gBAAO,mBAAmB;gBAAM,gBAAgB;gBAAO,eAAe;YAAM;YAC1G,iBAAiB;gBAAE,WAAW;gBAAQ,mBAAmB;gBAAI,gBAAgB;gBAAM,eAAe;YAAO;YACzG,WAAW;gBAAE,WAAW;gBAAM,mBAAmB;gBAAI,gBAAgB;gBAAM,eAAe;YAAK;QACjG;QAEA,OAAO,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU;IACrD;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ;QAEb,aAAa;QACb,IAAI;YACF,MAAM,OAAO;gBACX,SAAS,OAAO;gBAChB,QAAQ;gBACR,UAAU,SAAS,QAAQ;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,YAAY,CAAC,OAAc,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,YAAY,CAAC,OAAc,CAAC;gBAC1B,GAAG,IAAI;gBACP,UAAU;oBACR,GAAG,KAAK,QAAQ;oBAChB,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,CAAA;YAChB,IAAI,KAAK,QAAQ,CAAC,YAAY;gBAC5B,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM;YAChC,OAAO;gBACL,OAAO;uBAAI;oBAAM;iBAAU;YAC7B;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,eAAe,MAAM,KAAK,gBAAgB,MAAM,EAAE;YACpD,kBAAkB,EAAE;QACtB,OAAO;YACL,kBAAkB;mBAAI;aAAgB;QACxC;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS,OAAO,IAAI,eAAe,MAAM,KAAK,GAAG;YACpD,MAAM;YACN;QACF;QAEA,sBAAsB;QACtB,qBAAqB;QAErB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,SAAS,SAAS,OAAO;oBACzB,QAAQ,SAAS,MAAM;oBACvB,gBAAgB;gBAClB;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,qBAAqB;YACvB,OAAO;gBACL,qBAAqB;oBACnB,QAAQ;oBACR,OAAO,OAAO,KAAK,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,qBAAqB;gBACnB,QAAQ;gBACR,OAAO;YACT;QACF,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,QAAQ,8BAA8B;;;;;;sCAEzC,6LAAC,qIAAA,CAAA,oBAAiB;sCACf,QACG,mDACA;;;;;;;;;;;;8BAKR,6LAAC;oBAAK,UAAU;;sCACd,6LAAC,mIAAA,CAAA,OAAI;4BAAC,cAAa;4BAAQ,WAAU;;8CACnC,6LAAC,mIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAQ;;;;;;sDAC3B,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;sDAC9B,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAO;;;;;;;;;;;;8CAG5B,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,QAAQ;4DACxB,eAAe,CAAC,QAAU,eAAe,YAAY;;8EAErD,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,6LAAC,qIAAA,CAAA,gBAAa;8EACX,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,qIAAA,CAAA,aAAU;4EAAsB,OAAO,SAAS,KAAK;sFACpD,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;kGAAM,SAAS,KAAK;;;;;;kGACrB,6LAAC;wFAAK,WAAU;kGACb,SAAS,WAAW;;;;;;;;;;;;2EAJV,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;8DAavC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;;0EAClC,6LAAC,qIAAA,CAAA,SAAM;gEACL,IAAG;gEACH,SAAS,SAAS,QAAQ;gEAC1B,iBAAiB,CAAC,UAAY,eAAe,YAAY;;;;;;4DACzD;;;;;;;;;;;;;;;;;;sDAMR,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,eAAe,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC7D,aAAY;oDACZ,MAAM;;;;;;;;;;;;sDAIV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAM,aAAa,SAAS;4DAC5B,OAAO,SAAS,MAAM;4DACtB,UAAU,CAAC,IAAM,eAAe,UAAU,EAAE,MAAM,CAAC,KAAK;4DACxD,aAAY;;;;;;sEAEd,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,cAAc,CAAC;sEAE7B,2BAAa,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAAe,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAKpE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;oDACzD,aAAY;oDACZ,QAAQ;;;;;;8DAEV,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;wCAM9C,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;sEAER,eAAe,MAAM,KAAK,gBAAgB,MAAM,GAAG,qBAAqB;;;;;;;;;;;;8DAI7E,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,0BACpB,6LAAC;4DAAoB,WAAU;;8EAC7B,6LAAC;oEACC,MAAK;oEACL,IAAI,CAAC,MAAM,EAAE,WAAW;oEACxB,SAAS,eAAe,QAAQ,CAAC;oEACjC,UAAU,IAAM,kBAAkB;oEAClC,WAAU;;;;;;8EAEZ,6LAAC,oIAAA,CAAA,QAAK;oEACJ,SAAS,CAAC,MAAM,EAAE,WAAW;oEAC7B,WAAU;8EAET;;;;;;;2DAZK;;;;;;;;;;8DAkBd,6LAAC;oDAAE,WAAU;;wDAAgC;wDACjC,eAAe,MAAM;wDAAC;wDAAK,gBAAgB,MAAM;wDAAC;;;;;;;;;;;;;;;;;;;8CAMpE,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;;oEAAC;oEAAe,SAAS,QAAQ,CAAC,WAAW;;;;;;;0EACnD,6LAAC,qIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,SAAS,QAAQ,CAAC,WAAW;iEAAC;gEACtC,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,eAAe;gEAC1D,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;0EAEZ,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAK/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;;oEAAC;oEAAqB,SAAS,QAAQ,CAAC,SAAS;;;;;;;0EACvD,6LAAC,qIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,SAAS,QAAQ,CAAC,SAAS;iEAAC;gEACpC,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,aAAa;gEACxD,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;;oEAAC;oEAAQ,SAAS,QAAQ,CAAC,IAAI;;;;;;;0EACrC,6LAAC,qIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,SAAS,QAAQ,CAAC,IAAI;iEAAC;gEAC/B,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,QAAQ;gEACnD,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;;4EAAC;4EAAgB,SAAS,QAAQ,CAAC,gBAAgB;;;;;;;kFACzD,6LAAC,qIAAA,CAAA,SAAM;wEACL,OAAO;4EAAC,SAAS,QAAQ,CAAC,gBAAgB;yEAAC;wEAC3C,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,oBAAoB;wEAC/D,KAAK;wEACL,KAAK;wEACL,MAAM;wEACN,WAAU;;;;;;;;;;;;0EAId,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;;4EAAC;4EAAe,SAAS,QAAQ,CAAC,eAAe;;;;;;;kFACvD,6LAAC,qIAAA,CAAA,SAAM;wEACL,OAAO;4EAAC,SAAS,QAAQ,CAAC,eAAe;yEAAC;wEAC1C,eAAe,CAAC,CAAC,MAAM,GAAK,eAAe,mBAAmB;wEAC9D,KAAK;wEACL,KAAK;wEACL,MAAM;wEACN,WAAU;;;;;;;;;;;;;;;;;;kEAKhB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAe;;;;;;0EAC9B,6LAAC,uIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,OAAO,SAAS,QAAQ,CAAC,YAAY;gEACrC,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAC9D,aAAY;gEACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOhB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;;sDAElC,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC;gEAAS,WAAU;;;;;;4DAAY;;;;;;;;;;;;8DAIpC,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAG7C,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS;oEACT,UAAU,sBAAsB,CAAC,SAAS,OAAO,IAAI,eAAe,MAAM,KAAK;8EAE9E,mCACC;;0FACE,6LAAC;gFAAI,WAAU;;;;;;4EAAyE;;qGAI1F;;0FACE,6LAAC;gFAAS,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;wDAO5C,mCACC,6LAAC;4DAAI,WAAW,CAAC,sBAAsB,EACrC,kBAAkB,MAAM,KAAK,WAAW,iCACxC,kBAAkB,MAAM,KAAK,YAAY,mCACzC,4BACA;;8EACA,6LAAC;oEAAI,WAAU;;wEACZ,kBAAkB,MAAM,KAAK,0BAAY,6LAAC;4EAAY,WAAU;;;;;;wEAChE,kBAAkB,MAAM,KAAK,2BAAa,6LAAC;4EAAc,WAAU;;;;;;wEACnE,kBAAkB,MAAM,KAAK,yBAAW,6LAAC;4EAAQ,WAAU;;;;;;sFAC5D,6LAAC;4EAAK,WAAW,CAAC,cAAc,EAC9B,kBAAkB,MAAM,KAAK,WAAW,mBACxC,kBAAkB,MAAM,KAAK,YAAY,oBACzC,gBACA;sFACC,kBAAkB,MAAM,KAAK,WAAW,wBACxC,kBAAkB,MAAM,KAAK,YAAY,wBACzC;;;;;;;;;;;;gEAIJ,kBAAkB,YAAY,kBAC7B,6LAAC;oEAAE,WAAU;;wEAAqC;wEAChC,kBAAkB,YAAY;wEAAC;;;;;;;gEAIlD,kBAAkB,KAAK,kBACtB,6LAAC;oEAAE,WAAU;;wEAA4B;wEACjC,kBAAkB,KAAK;;;;;;;gEAIhC,kBAAkB,MAAM,IAAI,kBAAkB,MAAM,CAAC,MAAM,GAAG,mBAC7D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAA2B;;;;;;sFACzC,6LAAC;4EAAI,WAAU;sFACZ,kBAAkB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAY,sBACzC,6LAAC;oFAAgB,WAAU;;sGACzB,6LAAC;sGAAM,MAAM,IAAI;;;;;;sGACjB,6LAAC;4FAAI,WAAU;;8GACb,6LAAC;oGAAK,WAAW,CAAC,0BAA0B,EAC1C,MAAM,MAAM,KAAK,YAAY,gCAC7B,2BACA;8GACC,MAAM,MAAM,KAAK,YAAY,SAAS;;;;;;8GAEzC,6LAAC;oGAAK,WAAU;;wGAAyB,MAAM,YAAY;wGAAC;;;;;;;;;;;;;;mFATtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAqBzB,uBACC,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAU;;;;;;;;;;;8DAEjC,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAa;;;;;;8EAC5B,6LAAC,uIAAA,CAAA,WAAQ;oEACP,IAAG;oEACH,OAAO;oEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oEAC7C,aAAY;oEACZ,MAAM;;;;;;;;;;;;sEAIV,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS;4DACT,UAAU,aAAa,CAAC,WAAW,IAAI;4DACvC,WAAU;;8EAEV,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,YAAY,qBAAqB;;;;;;;wDAGnC,MAAM,UAAU,kBACf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAS,MAAM,UAAU,CAAC,OAAO,GAAG,YAAY;sFACpD,MAAM,UAAU,CAAC,OAAO,GAAG,QAAQ;;;;;;sFAEtC,6LAAC;4EAAK,WAAU;;gFACb,MAAM,UAAU,CAAC,YAAY;gFAAC;;;;;;;;;;;;;gEAGlC,MAAM,UAAU,CAAC,KAAK,kBACrB,6LAAC;oEAAE,WAAU;8EAA4B,MAAM,UAAU,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU/E,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;;sDAC/C,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGhC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;;sDAC9B,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;GAzrBgB;KAAA", "debugId": null}}, {"offset": {"line": 3984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/dashboard/admin/ai-models/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { AdminDashboardHeader } from '@/components/admin/AdminDashboardHeader'\nimport { AIModelCard } from '@/components/admin/AIModelCard'\nimport { AIModelForm } from '@/components/admin/AIModelForm'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog'\nimport { toast } from 'sonner'\nimport { \n  Brain, \n  Plus, \n  Search, \n  Filter, \n  BarChart3, \n  Activity, \n  DollarSign, \n  Zap,\n  RefreshCw,\n  Settings,\n  TestTube\n} from 'lucide-react'\nimport { AIModel, CreateModelRequest, UpdateModelRequest } from '@/types/ai-models'\n\nexport default function AIModelsPage() {\n  const { user, profile } = useAuth()\n  const [models, setModels] = useState<AIModel[]>([])\n  const [filteredModels, setFilteredModels] = useState<AIModel[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedProvider, setSelectedProvider] = useState<string>('all')\n  const [selectedType, setSelectedType] = useState<string>('all')\n  const [selectedStatus, setSelectedStatus] = useState<string>('all')\n  const [showForm, setShowForm] = useState(false)\n  const [editingModel, setEditingModel] = useState<AIModel | undefined>()\n  const [deletingModel, setDeletingModel] = useState<AIModel | undefined>()\n  const [stats, setStats] = useState<any>({})\n\n  // جلب النماذج\n  const fetchModels = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/ai-models?include_inactive=true')\n      const data = await response.json()\n      \n      if (response.ok) {\n        setModels(data.models)\n        setStats(data.stats)\n      } else {\n        toast.error(data.error || 'خطأ في جلب النماذج')\n      }\n    } catch (error) {\n      console.error('Error fetching models:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // تطبيق الفلاتر\n  useEffect(() => {\n    let filtered = models\n\n    // البحث النصي\n    if (searchTerm) {\n      filtered = filtered.filter(model =>\n        model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        model.description.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // فلترة حسب المقدم\n    if (selectedProvider !== 'all') {\n      filtered = filtered.filter(model => model.provider === selectedProvider)\n    }\n\n    // فلترة حسب النوع\n    if (selectedType !== 'all') {\n      filtered = filtered.filter(model => model.type === selectedType)\n    }\n\n    // فلترة حسب الحالة\n    if (selectedStatus !== 'all') {\n      if (selectedStatus === 'active') {\n        filtered = filtered.filter(model => model.isActive)\n      } else if (selectedStatus === 'inactive') {\n        filtered = filtered.filter(model => !model.isActive)\n      } else {\n        filtered = filtered.filter(model => model.status === selectedStatus)\n      }\n    }\n\n    setFilteredModels(filtered)\n  }, [models, searchTerm, selectedProvider, selectedType, selectedStatus])\n\n  useEffect(() => {\n    fetchModels()\n  }, [])\n\n  // حفظ النموذج\n  const handleSaveModel = async (data: CreateModelRequest | UpdateModelRequest) => {\n    try {\n      const url = editingModel ? `/api/ai-models/${editingModel.id}` : '/api/ai-models'\n      const method = editingModel ? 'PUT' : 'POST'\n      \n      const response = await fetch(url, {\n        method,\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(data)\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        toast.success(result.message)\n        setShowForm(false)\n        setEditingModel(undefined)\n        fetchModels()\n      } else {\n        toast.error(result.error)\n      }\n    } catch (error) {\n      console.error('Error saving model:', error)\n      toast.error('خطأ في حفظ النموذج')\n    }\n  }\n\n  // حذف النموذج\n  const handleDeleteModel = async () => {\n    if (!deletingModel) return\n\n    try {\n      const response = await fetch(`/api/ai-models/${deletingModel.id}`, {\n        method: 'DELETE'\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        toast.success(result.message)\n        setDeletingModel(undefined)\n        fetchModels()\n      } else {\n        toast.error(result.error)\n      }\n    } catch (error) {\n      console.error('Error deleting model:', error)\n      toast.error('خطأ في حذف النموذج')\n    }\n  }\n\n  // تفعيل/إلغاء تفعيل النموذج\n  const handleToggleActive = async (model: AIModel, isActive: boolean) => {\n    try {\n      const response = await fetch(`/api/ai-models/${model.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ isActive })\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        toast.success(result.message)\n        fetchModels()\n      } else {\n        toast.error(result.error)\n      }\n    } catch (error) {\n      console.error('Error toggling model:', error)\n      toast.error('خطأ في تحديث النموذج')\n    }\n  }\n\n  // اختبار النموذج\n  const handleTestModel = async (model: AIModel) => {\n    try {\n      const response = await fetch('/api/ai-models/test', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          modelId: model.id,\n          prompt: 'مرحباً، هذا اختبار للنموذج'\n        })\n      })\n      \n      const result = await response.json()\n      \n      if (response.ok) {\n        if (result.success) {\n          toast.success(`نجح اختبار النموذج (${result.responseTime}ms)`)\n        } else {\n          toast.error(`فشل اختبار النموذج: ${result.error}`)\n        }\n        fetchModels()\n      } else {\n        toast.error('خطأ في اختبار النموذج')\n      }\n    } catch (error) {\n      console.error('Error testing model:', error)\n      toast.error('خطأ في اختبار النموذج')\n    }\n  }\n\n  const providers = Array.from(new Set(models.map(m => m.provider)))\n  const types = Array.from(new Set(models.map(m => m.type)))\n\n  return (\n    <ProtectedRoute requiredRole={UserRole.ADMIN}>\n      <div className=\"min-h-screen bg-background\">\n        <AdminDashboardHeader />\n        \n        <div className=\"container mx-auto px-4 py-8\">\n          {/* العنوان والإحصائيات */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div>\n              <h1 className=\"text-3xl font-bold arabic-text flex items-center gap-3\">\n                <Brain className=\"h-8 w-8 text-primary\" />\n                إدارة مقدمي خدمات الذكاء الاصطناعي\n              </h1>\n              <p className=\"text-muted-foreground mt-2\">\n                إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم\n              </p>\n            </div>\n            \n            <div className=\"flex gap-2\">\n              <Button onClick={() => fetchModels()} variant=\"outline\">\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                تحديث\n              </Button>\n              <Button onClick={() => setShowForm(true)}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                إضافة مقدم خدمة\n              </Button>\n            </div>\n          </div>\n\n          {/* بطاقات الإحصائيات */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">مقدمو الخدمة</CardTitle>\n                <Brain className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.total || 0}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  {stats.active || 0} نشط، {stats.inactive || 0} غير نشط\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">مقدمو الخدمة</CardTitle>\n                <Settings className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{providers.length}</div>\n                <div className=\"flex flex-wrap gap-1 mt-2\">\n                  {providers.slice(0, 3).map(provider => (\n                    <Badge key={provider} variant=\"secondary\" className=\"text-xs\">\n                      {provider}\n                    </Badge>\n                  ))}\n                  {providers.length > 3 && (\n                    <Badge variant=\"outline\" className=\"text-xs\">\n                      +{providers.length - 3}\n                    </Badge>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">إجمالي الطلبات</CardTitle>\n                <Activity className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  {models.reduce((sum, m) => sum + m.usage.totalRequests, 0).toLocaleString()}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  عبر جميع النماذج\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">إجمالي التكلفة</CardTitle>\n                <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  ${models.reduce((sum, m) => sum + m.usage.totalCost, 0).toFixed(2)}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  هذا الشهر\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* أدوات البحث والفلترة */}\n          <Card className=\"mb-6\">\n            <CardContent className=\"pt-6\">\n              <div className=\"flex flex-col md:flex-row gap-4\">\n                <div className=\"flex-1\">\n                  <div className=\"relative\">\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                    <Input\n                      placeholder=\"البحث في النماذج...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"pl-10\"\n                    />\n                  </div>\n                </div>\n                \n                <Select value={selectedProvider} onValueChange={setSelectedProvider}>\n                  <SelectTrigger className=\"w-full md:w-48\">\n                    <SelectValue placeholder=\"مقدم الخدمة\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">جميع المقدمين</SelectItem>\n                    {providers.map(provider => (\n                      <SelectItem key={provider} value={provider}>\n                        {provider}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n\n                <Select value={selectedType} onValueChange={setSelectedType}>\n                  <SelectTrigger className=\"w-full md:w-48\">\n                    <SelectValue placeholder=\"نوع النموذج\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">جميع الأنواع</SelectItem>\n                    {types.map(type => (\n                      <SelectItem key={type} value={type}>\n                        {type}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n\n                <Select value={selectedStatus} onValueChange={setSelectedStatus}>\n                  <SelectTrigger className=\"w-full md:w-48\">\n                    <SelectValue placeholder=\"الحالة\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">جميع الحالات</SelectItem>\n                    <SelectItem value=\"active\">نشط</SelectItem>\n                    <SelectItem value=\"inactive\">غير نشط</SelectItem>\n                    <SelectItem value=\"error\">خطأ</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* قائمة النماذج */}\n          {loading ? (\n            <div className=\"text-center py-12\">\n              <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n              <p>جاري تحميل النماذج...</p>\n            </div>\n          ) : filteredModels.length === 0 ? (\n            <Card>\n              <CardContent className=\"text-center py-12\">\n                <Brain className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground\" />\n                <h3 className=\"text-lg font-semibold mb-2\">لا توجد مقدمو خدمة</h3>\n                <p className=\"text-muted-foreground mb-4\">\n                  {searchTerm || selectedProvider !== 'all' || selectedType !== 'all' || selectedStatus !== 'all'\n                    ? 'لا توجد مقدمو خدمة تطابق معايير البحث'\n                    : 'لم يتم إضافة أي مقدم خدمة بعد'\n                  }\n                </p>\n                <Button onClick={() => setShowForm(true)}>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  إضافة مقدم خدمة جديد\n                </Button>\n              </CardContent>\n            </Card>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {filteredModels.map((model) => (\n                <AIModelCard\n                  key={model.id}\n                  model={model}\n                  onEdit={(model) => {\n                    setEditingModel(model)\n                    setShowForm(true)\n                  }}\n                  onDelete={(model) => setDeletingModel(model)}\n                  onTest={handleTestModel}\n                  onToggleActive={handleToggleActive}\n                  onViewUsage={(model) => {\n                    // TODO: فتح صفحة الإحصائيات\n                    toast.info('سيتم إضافة صفحة الإحصائيات قريباً')\n                  }}\n                  onSettings={(model) => {\n                    setEditingModel(model)\n                    setShowForm(true)\n                  }}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* نموذج إضافة/تحرير */}\n        <AIModelForm\n          model={editingModel}\n          isOpen={showForm}\n          onClose={() => {\n            setShowForm(false)\n            setEditingModel(undefined)\n          }}\n          onSave={handleSaveModel}\n          onTest={async (data) => {\n            await handleTestModel(editingModel!)\n          }}\n        />\n\n        {/* تأكيد الحذف */}\n        <AlertDialog open={!!deletingModel} onOpenChange={() => setDeletingModel(undefined)}>\n          <AlertDialogContent>\n            <AlertDialogHeader>\n              <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>\n              <AlertDialogDescription>\n                هل أنت متأكد من حذف النموذج \"{deletingModel?.name}\"؟\n                هذا الإجراء لا يمكن التراجع عنه.\n              </AlertDialogDescription>\n            </AlertDialogHeader>\n            <AlertDialogFooter>\n              <AlertDialogCancel>إلغاء</AlertDialogCancel>\n              <AlertDialogAction onClick={handleDeleteModel} className=\"bg-destructive text-destructive-foreground\">\n                حذف\n              </AlertDialogAction>\n            </AlertDialogFooter>\n          </AlertDialogContent>\n        </AlertDialog>\n      </div>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAOA;AAUA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAhCA;;;;;;;;;;;;;;;;AA+Ce,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAEzC,cAAc;IACd,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU,KAAK,MAAM;gBACrB,SAAS,KAAK,KAAK;YACrB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW;YAEf,cAAc;YACd,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;8CAAC,CAAA,QACzB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;YAEnE;YAEA,mBAAmB;YACnB,IAAI,qBAAqB,OAAO;gBAC9B,WAAW,SAAS,MAAM;8CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;;YACzD;YAEA,kBAAkB;YAClB,IAAI,iBAAiB,OAAO;gBAC1B,WAAW,SAAS,MAAM;8CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;;YACrD;YAEA,mBAAmB;YACnB,IAAI,mBAAmB,OAAO;gBAC5B,IAAI,mBAAmB,UAAU;oBAC/B,WAAW,SAAS,MAAM;kDAAC,CAAA,QAAS,MAAM,QAAQ;;gBACpD,OAAO,IAAI,mBAAmB,YAAY;oBACxC,WAAW,SAAS,MAAM;kDAAC,CAAA,QAAS,CAAC,MAAM,QAAQ;;gBACrD,OAAO;oBACL,WAAW,SAAS,MAAM;kDAAC,CAAA,QAAS,MAAM,MAAM,KAAK;;gBACvD;YACF;YAEA,kBAAkB;QACpB;iCAAG;QAAC;QAAQ;QAAY;QAAkB;QAAc;KAAe;IAEvE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,cAAc;IACd,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,MAAM,eAAe,CAAC,eAAe,EAAE,aAAa,EAAE,EAAE,GAAG;YACjE,MAAM,SAAS,eAAe,QAAQ;YAEtC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B,YAAY;gBACZ,gBAAgB;gBAChB;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,cAAc;IACd,MAAM,oBAAoB;QACxB,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,cAAc,EAAE,EAAE,EAAE;gBACjE,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B,iBAAiB;gBACjB;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB,OAAO,OAAgB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,iBAAiB;IACjB,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,MAAM,EAAE;oBACjB,QAAQ;gBACV;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,IAAI,OAAO,OAAO,EAAE;oBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,oBAAoB,EAAE,OAAO,YAAY,CAAC,GAAG,CAAC;gBAC/D,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,oBAAoB,EAAE,OAAO,KAAK,EAAE;gBACnD;gBACA;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,YAAY,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IAC/D,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;IAEvD,qBACE,6LAAC,+IAAA,CAAA,iBAAc;QAAC,cAAc,uHAAA,CAAA,WAAQ,CAAC,KAAK;kBAC1C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,sJAAA,CAAA,uBAAoB;;;;;8BAErB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAG5C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM;4CAAe,SAAQ;;8DAC5C,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,YAAY;;8DACjC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DAAsB,MAAM,KAAK,IAAI;;;;;;8DACpD,6LAAC;oDAAE,WAAU;;wDACV,MAAM,MAAM,IAAI;wDAAE;wDAAO,MAAM,QAAQ,IAAI;wDAAE;;;;;;;;;;;;;;;;;;;8CAKpD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DAAsB,UAAU,MAAM;;;;;;8DACrD,6LAAC;oDAAI,WAAU;;wDACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,yBACzB,6LAAC,oIAAA,CAAA,QAAK;gEAAgB,SAAQ;gEAAY,WAAU;0EACjD;+DADS;;;;;wDAIb,UAAU,MAAM,GAAG,mBAClB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAU;gEACzC,UAAU,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;8CAO/B,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DACZ,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,GAAG,cAAc;;;;;;8DAE3E,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;;wDAAqB;wDAChC,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC;;;;;;;8DAElE,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;sCAQnD,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;;;;;;sDAKhB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAkB,eAAe;;8DAC9C,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;wDACvB,UAAU,GAAG,CAAC,CAAA,yBACb,6LAAC,qIAAA,CAAA,aAAU;gEAAgB,OAAO;0EAC/B;+DADc;;;;;;;;;;;;;;;;;sDAOvB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAc,eAAe;;8DAC1C,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;wDACvB,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,qIAAA,CAAA,aAAU;gEAAY,OAAO;0EAC3B;+DADc;;;;;;;;;;;;;;;;;sDAOvB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAgB,eAAe;;8DAC5C,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQnC,wBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;8CAAE;;;;;;;;;;;mCAEH,eAAe,MAAM,KAAK,kBAC5B,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDACV,cAAc,qBAAqB,SAAS,iBAAiB,SAAS,mBAAmB,QACtF,0CACA;;;;;;kDAGN,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,YAAY;;0DACjC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;iDAMvC,6LAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC,6IAAA,CAAA,cAAW;oCAEV,OAAO;oCACP,QAAQ,CAAC;wCACP,gBAAgB;wCAChB,YAAY;oCACd;oCACA,UAAU,CAAC,QAAU,iBAAiB;oCACtC,QAAQ;oCACR,gBAAgB;oCAChB,aAAa,CAAC;wCACZ,4BAA4B;wCAC5B,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oCACb;oCACA,YAAY,CAAC;wCACX,gBAAgB;wCAChB,YAAY;oCACd;mCAhBK,MAAM,EAAE;;;;;;;;;;;;;;;;8BAwBvB,6LAAC,6IAAA,CAAA,cAAW;oBACV,OAAO;oBACP,QAAQ;oBACR,SAAS;wBACP,YAAY;wBACZ,gBAAgB;oBAClB;oBACA,QAAQ;oBACR,QAAQ,OAAO;wBACb,MAAM,gBAAgB;oBACxB;;;;;;8BAIF,6LAAC,8IAAA,CAAA,cAAW;oBAAC,MAAM,CAAC,CAAC;oBAAe,cAAc,IAAM,iBAAiB;8BACvE,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;0CACjB,6LAAC,8IAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,6LAAC,8IAAA,CAAA,yBAAsB;;4CAAC;4CACQ,eAAe;4CAAK;;;;;;;;;;;;;0CAItD,6LAAC,8IAAA,CAAA,oBAAiB;;kDAChB,6LAAC,8IAAA,CAAA,oBAAiB;kDAAC;;;;;;kDACnB,6LAAC,8IAAA,CAAA,oBAAiB;wCAAC,SAAS;wCAAmB,WAAU;kDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpH;GAzawB;;QACI,kIAAA,CAAA,UAAO;;;KADX", "debugId": null}}]}