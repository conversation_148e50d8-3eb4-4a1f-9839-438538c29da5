'use client'

import { useState, useEffect } from 'react'
import { AIModel, AIProvider, ModelType, CreateModelRequest, UpdateModelRequest } from '@/types/ai-models'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Eye, EyeOff, TestTube, Save, X } from 'lucide-react'

interface AIModelFormProps {
  model?: AIModel
  isOpen: boolean
  onClose: () => void
  onSave: (data: CreateModelRequest | UpdateModelRequest) => Promise<void>
  onTest?: (data: any) => Promise<void>
}

const PROVIDERS: { value: AIProvider; label: string; description: string }[] = [
  { value: 'openai', label: 'OpenAI', description: 'GPT, DALL-E, Whisper' },
  { value: 'anthropic', label: 'Anthropic', description: 'Claude Models' },
  { value: 'google', label: 'Google', description: 'Gemini, PaLM' },
  { value: 'meta', label: 'Meta', description: 'LLaMA Models' },
  { value: 'stability', label: 'Stability AI', description: 'Stable Diffusion' },
  { value: 'cohere', label: 'Cohere', description: 'Command Models' },
  { value: 'huggingface', label: 'Hugging Face', description: 'Open Source Models' },
  { value: 'deepseek', label: 'DeepSeek', description: 'DeepSeek Models' }
]

const MODEL_TYPES: { value: ModelType; label: string; icon: string }[] = [
  { value: 'text', label: 'نص', icon: '📝' },
  { value: 'image', label: 'صورة', icon: '🖼️' },
  { value: 'audio', label: 'صوت', icon: '🎵' },
  { value: 'multimodal', label: 'متعدد الوسائط', icon: '🔄' },
  { value: 'code', label: 'كود', icon: '💻' },
  { value: 'embedding', label: 'تضمين', icon: '🔗' }
]

export function AIModelForm({ model, isOpen, onClose, onSave, onTest }: AIModelFormProps) {
  const [formData, setFormData] = useState<any>({
    name: '',
    provider: 'openai' as AIProvider,
    type: 'text' as ModelType,
    description: '',
    apiKey: '',
    apiEndpoint: '',
    isActive: true,
    settings: {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      systemPrompt: ''
    }
  })
  
  const [showApiKey, setShowApiKey] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [testPrompt, setTestPrompt] = useState('مرحباً، كيف يمكنني مساعدتك؟')

  useEffect(() => {
    if (model) {
      setFormData({
        name: model.name,
        provider: model.provider,
        type: model.type,
        description: model.description || '',
        apiKey: model.apiKey || '',
        apiEndpoint: model.apiEndpoint || '',
        isActive: model.isActive,
        settings: { ...model.settings }
      })
    } else {
      // إعادة تعيين النموذج للإضافة الجديدة
      setFormData({
        name: '',
        provider: 'openai' as AIProvider,
        type: 'text' as ModelType,
        description: '',
        apiKey: '',
        apiEndpoint: '',
        isActive: true,
        settings: {
          temperature: 0.7,
          maxTokens: 2048,
          topP: 1,
          frequencyPenalty: 0,
          presencePenalty: 0,
          systemPrompt: ''
        }
      })
    }
  }, [model, isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Error saving model:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTest = async () => {
    if (!onTest) return
    
    setIsLoading(true)
    try {
      await onTest({
        modelId: model?.id,
        prompt: testPrompt,
        settings: formData.settings
      })
    } catch (error) {
      console.error('Error testing model:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateFormData = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateSettings = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      settings: {
        ...prev.settings,
        [field]: value
      }
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="arabic-text">
            {model ? 'تحرير نموذج الذكاء الاصطناعي' : 'إضافة نموذج جديد'}
          </DialogTitle>
          <DialogDescription>
            {model 
              ? 'قم بتحديث إعدادات النموذج وخصائصه'
              : 'أضف نموذج ذكاء اصطناعي جديد للمنصة'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">المعلومات الأساسية</TabsTrigger>
              <TabsTrigger value="settings">الإعدادات</TabsTrigger>
              <TabsTrigger value="test">الاختبار</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">اسم النموذج *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => updateFormData('name', e.target.value)}
                    placeholder="مثال: GPT-4 Turbo"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="provider">مقدم الخدمة *</Label>
                  <Select 
                    value={formData.provider} 
                    onValueChange={(value) => updateFormData('provider', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {PROVIDERS.map((provider) => (
                        <SelectItem key={provider.value} value={provider.value}>
                          <div className="flex flex-col">
                            <span>{provider.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {provider.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">نوع النموذج *</Label>
                  <Select 
                    value={formData.type} 
                    onValueChange={(value) => updateFormData('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {MODEL_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            <span>{type.icon}</span>
                            <span>{type.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="isActive" className="flex items-center gap-2">
                    <Switch
                      id="isActive"
                      checked={formData.isActive}
                      onCheckedChange={(checked) => updateFormData('isActive', checked)}
                    />
                    نشط
                  </Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  placeholder="وصف مختصر للنموذج وقدراته"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="apiKey">مفتاح API</Label>
                <div className="relative">
                  <Input
                    id="apiKey"
                    type={showApiKey ? 'text' : 'password'}
                    value={formData.apiKey}
                    onChange={(e) => updateFormData('apiKey', e.target.value)}
                    placeholder="أدخل مفتاح API"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-2 top-1/2 -translate-y-1/2"
                    onClick={() => setShowApiKey(!showApiKey)}
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="apiEndpoint">نقطة النهاية (اختياري)</Label>
                <Input
                  id="apiEndpoint"
                  value={formData.apiEndpoint}
                  onChange={(e) => updateFormData('apiEndpoint', e.target.value)}
                  placeholder="https://api.example.com/v1"
                />
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">إعدادات التوليد</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label>درجة الحرارة: {formData.settings.temperature}</Label>
                    <Slider
                      value={[formData.settings.temperature]}
                      onValueChange={([value]) => updateSettings('temperature', value)}
                      max={2}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      قيم أعلى تعني إجابات أكثر إبداعاً وعشوائية
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>الحد الأقصى للرموز: {formData.settings.maxTokens}</Label>
                    <Slider
                      value={[formData.settings.maxTokens]}
                      onValueChange={([value]) => updateSettings('maxTokens', value)}
                      max={8192}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Top P: {formData.settings.topP}</Label>
                    <Slider
                      value={[formData.settings.topP]}
                      onValueChange={([value]) => updateSettings('topP', value)}
                      max={1}
                      min={0}
                      step={0.01}
                      className="w-full"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>عقوبة التكرار: {formData.settings.frequencyPenalty}</Label>
                      <Slider
                        value={[formData.settings.frequencyPenalty]}
                        onValueChange={([value]) => updateSettings('frequencyPenalty', value)}
                        max={2}
                        min={0}
                        step={0.1}
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>عقوبة الحضور: {formData.settings.presencePenalty}</Label>
                      <Slider
                        value={[formData.settings.presencePenalty]}
                        onValueChange={([value]) => updateSettings('presencePenalty', value)}
                        max={2}
                        min={0}
                        step={0.1}
                        className="w-full"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="systemPrompt">الرسالة النظامية</Label>
                    <Textarea
                      id="systemPrompt"
                      value={formData.settings.systemPrompt}
                      onChange={(e) => updateSettings('systemPrompt', e.target.value)}
                      placeholder="أنت مساعد ذكي مفيد ومهذب..."
                      rows={4}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="test" className="space-y-4">
              {model && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">اختبار النموذج</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="testPrompt">النص التجريبي</Label>
                      <Textarea
                        id="testPrompt"
                        value={testPrompt}
                        onChange={(e) => setTestPrompt(e.target.value)}
                        placeholder="أدخل نص لاختبار النموذج"
                        rows={3}
                      />
                    </div>

                    <Button 
                      type="button" 
                      onClick={handleTest}
                      disabled={isLoading || !testPrompt.trim()}
                      className="w-full"
                    >
                      <TestTube className="h-4 w-4 mr-2" />
                      {isLoading ? 'جاري الاختبار...' : 'اختبار النموذج'}
                    </Button>

                    {model.testResult && (
                      <div className="mt-4 p-4 border rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant={model.testResult.success ? "default" : "destructive"}>
                            {model.testResult.success ? 'نجح' : 'فشل'}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {model.testResult.responseTime}ms
                          </span>
                        </div>
                        {model.testResult.error && (
                          <p className="text-sm text-destructive">{model.testResult.error}</p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              <X className="h-4 w-4 mr-2" />
              إلغاء
            </Button>
            <Button type="submit" disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'جاري الحفظ...' : 'حفظ'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
