{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["export type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,qHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,8CAA8C;YAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,eAAe;gBAAC;gBAAM;gBAAM;aAAK,CAAC,QAAQ,CAAC,cAAc;gBAC3D,UAAU;YACZ;QACF;mCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,OAAO;QAErC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;GApCgB", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;;;AAPA;;;;;AAcO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D;GAzBgB;;QACO,mJAAA,CAAA,WAAQ;;;KADf", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Languages, Check, Globe } from \"lucide-react\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguageInfo = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguageInfo()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <Globe className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenuLabel className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;;;AARA;;;;;;AAiBO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,yBAAyB;QAC7B,OAAO;YACL,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC1B,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,6LAAC,+IAAA,CAAA,wBAAqB;;;;;oBACrB,OAAO,OAAO,CAAC,qHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,6LAAC,+IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,6LAAC;oCAAK,WAAU;8CAAW,qHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAhBd;;;;;;;;;;;;;;;;;AAuBjB;GAnEgB;;QACmB,iIAAA,CAAA,iBAAc;;;KADjC", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case UserRole.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case UserRole.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case UserRole.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleLabel = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'مدير'\n      case UserRole.SCHOOL:\n        return 'مدرسة'\n      case UserRole.DELIVERY:\n        return 'شريك توصيل'\n      case UserRole.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard/student'\n\n    // توجيه كل دور إلى لوحة التحكم المخصصة له\n    switch (profile.role) {\n      case UserRole.ADMIN:\n        return '/dashboard/admin'\n      case UserRole.SCHOOL:\n        return '/dashboard/school'\n      case UserRole.DELIVERY:\n        return '/dashboard/delivery'\n      case UserRole.STUDENT:\n        return '/dashboard/student'\n      default:\n        return '/dashboard/student'\n    }\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\">User menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRoleLabel(profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem \n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAhBA;;;;;;;;AAkBO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,6LAAC,qIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,6LAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,0CAA0C;QAC1C,OAAQ,QAAQ,IAAI;YAClB,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,6LAAC;sDAAM,aAAa,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAItC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;8CACvC,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,+IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB;GA9HgB;;QACqB,kIAAA,CAAA,UAAO;QAC5B,iIAAA,CAAA,iBAAc;;;KAFd", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  Settings,\n  X\n} from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const { \n    notifications, \n    unreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification, \n    clearAll \n  } = useNotifications()\n  \n  const [isOpen, setIsOpen] = useState(false)\n\n  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {\n    markAsRead(notificationId)\n    if (actionUrl) {\n      window.location.href = actionUrl\n    }\n  }\n\n  const recentNotifications = notifications.slice(0, 10)\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      \n      <DropdownMenuContent \n        align=\"end\" \n        className=\"w-80 p-0\"\n        sideOffset={5}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h3 className=\"font-semibold arabic-text\">الإشعارات</h3>\n          <div className=\"flex items-center gap-2\">\n            {unreadCount > 0 && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={markAllAsRead}\n                className=\"h-8 px-2 text-xs arabic-text\"\n              >\n                <CheckCheck className=\"h-3 w-3 mr-1\" />\n                قراءة الكل\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <ScrollArea className=\"h-96\">\n          {recentNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n              <Bell className=\"h-12 w-12 text-gray-400 mb-3\" />\n              <p className=\"text-gray-500 arabic-text\">لا توجد إشعارات</p>\n              <p className=\"text-sm text-gray-400 arabic-text\">ستظهر إشعاراتك هنا</p>\n            </div>\n          ) : (\n            <div className=\"divide-y\">\n              {recentNotifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${\n                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''\n                  }`}\n                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* Icon */}\n                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${\n                      getNotificationColor(notification.priority)\n                    }`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <h4 className={`text-sm font-medium arabic-text ${\n                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        \n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 ml-2\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              removeNotification(notification.id)\n                            }}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatNotificationTime(notification.createdAt)}\n                        </span>\n\n                        {notification.actionText && notification.actionUrl && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleNotificationClick(notification.id, notification.actionUrl)\n                            }}\n                          >\n                            {notification.actionText}\n                            <ExternalLink className=\"h-3 w-3 mr-1\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Expiry Warning */}\n                      {notification.expiresAt && (\n                        <div className=\"mt-2\">\n                          <Badge variant=\"outline\" className=\"text-xs arabic-text\">\n                            ينتهي في {formatNotificationTime(notification.expiresAt)}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n\n        {/* Footer */}\n        {notifications.length > 0 && (\n          <>\n            <Separator />\n            <div className=\"p-3 flex items-center justify-between\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-xs arabic-text\"\n                asChild\n              >\n                <a href=\"/notifications\">عرض جميع الإشعارات</a>\n              </Button>\n              \n              {notifications.length > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAll}\n                  className=\"text-xs text-red-600 hover:text-red-700 arabic-text\"\n                >\n                  <Trash2 className=\"h-3 w-3 mr-1\" />\n                  حذف الكل\n                </Button>\n              )}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n// مكون إشعار منبثق للإشعارات الهامة\ninterface ToastNotificationProps {\n  notification: {\n    id: string\n    title: string\n    message: string\n    type: string\n    priority: string\n  }\n  onClose: () => void\n  onAction?: () => void\n}\n\nexport function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {\n  return (\n    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${\n      getNotificationColor(notification.priority as any)\n    } animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start gap-3\">\n        <div className=\"flex-shrink-0 text-lg\">\n          {getNotificationIcon(notification.type as any)}\n        </div>\n        \n        <div className=\"flex-1\">\n          <h4 className=\"font-medium arabic-text\">{notification.title}</h4>\n          <p className=\"text-sm mt-1 arabic-text\">{notification.message}</p>\n          \n          <div className=\"flex items-center gap-2 mt-3\">\n            {onAction && (\n              <Button size=\"sm\" onClick={onAction} className=\"arabic-text\">\n                عرض\n              </Button>\n            )}\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون عداد الإشعارات البسيط\nexport function NotificationBadge() {\n  const { unreadCount } = useNotifications()\n\n  if (unreadCount === 0) return null\n\n  return (\n    <Badge \n      variant=\"destructive\" \n      className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n    >\n      {unreadCount > 99 ? '99+' : unreadCount}\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;AAuBO,SAAS;;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,WAAW;QACX,IAAI,WAAW;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG;IAEnD,qBACE,6LAAC,+IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAGZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI3C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,oBAAoB,MAAM,KAAK,kBAC9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;iDAGnD,6LAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,6LAAC;oCAEC,WAAW,CAAC,6EAA6E,EACvF,CAAC,aAAa,MAAM,GAAG,sCAAsC,IAC7D;oCACF,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;8CAE9E,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAW,CAAC,4EAA4E,EAC3F,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,GAC1C;0DACC,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;0DAIxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAW,CAAC,gCAAgC,EAC9C,CAAC,aAAa,MAAM,GAAG,kCAAkC,oCACzD;0EACC,aAAa,KAAK;;;;;;0EAIrB,6LAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,6LAAC;wEAAI,WAAU;;;;;;kFAEjB,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;kFAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKnB,6LAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;kEAGvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,0IAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;4DAG/C,aAAa,UAAU,IAAI,aAAa,SAAS,kBAChD,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;gEACjE;;oEAEC,aAAa,UAAU;kFACxB,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;oDAM7B,aAAa,SAAS,kBACrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAsB;gEAC7C,CAAA,GAAA,0IAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAvE5D,aAAa,EAAE;;;;;;;;;;;;;;;oBAoF7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,6LAAC,wIAAA,CAAA,YAAS;;;;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,6LAAC;4CAAE,MAAK;sDAAiB;;;;;;;;;;;oCAG1B,cAAc,MAAM,GAAG,mBACtB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAjMgB;;QAQV,0IAAA,CAAA,mBAAgB;;;KARN;AAgNT,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,qBACE,6LAAC;QAAI,WAAW,CAAC,8DAA8D,EAC7E,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,EAC3C,4CAA4C,CAAC;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;8BAGxC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2B,aAAa,KAAK;;;;;;sCAC3D,6LAAC;4BAAE,WAAU;sCAA4B,aAAa,OAAO;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;;gCACZ,0BACC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAU,WAAU;8CAAc;;;;;;8CAI/D,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;MA5BgB;AA+BT,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEvC,IAAI,gBAAgB,GAAG,OAAO;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;kBAET,cAAc,KAAK,QAAQ;;;;;;AAGlC;IAbgB;;QACU,0IAAA,CAAA,mBAAgB;;;MAD1B", "debugId": null}}, {"offset": {"line": 1703, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon,\n  ChevronDown,\n  Grid3X3\n} from 'lucide-react'\n\n// أنواع البيانات لعناصر القائمة\ninterface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n}\n\n// نوع عنصر القائمة المعروض\ninterface NavItem {\n  href: string\n  label: string\n  icon?: React.ReactElement\n  target_type: 'internal' | 'external' | 'page'\n  subItems?: {\n    href: string\n    label: string\n    target_type: 'internal' | 'external' | 'page'\n  }[]\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [cartItemsCount, setCartItemsCount] = useState(0)\n  const [wishlistCount, setWishlistCount] = useState(0)\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n  // Mock cart and wishlist counts - replace with actual data\n  useEffect(() => {\n    // Simulate getting cart items from localStorage or API\n    const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]')\n    setCartItemsCount(cartItems.length)\n\n    const wishlistItems = JSON.parse(localStorage.getItem('wishlistItems') || '[]')\n    setWishlistCount(wishlistItems.length)\n  }, [])\n\n  // جلب عناصر القائمة من قاعدة البيانات\n  useEffect(() => {\n    const fetchMenuItems = async () => {\n      try {\n        setLoading(true)\n        // جلب جميع عناصر القائمة (الرئيسية والفرعية)\n        const response = await fetch('/api/menu-items')\n\n        if (response.ok) {\n          const data = await response.json()\n          setMenuItems(data.menuItems || [])\n        } else {\n          // في حالة فشل API، استخدم القائمة الافتراضية\n          console.warn('Failed to fetch menu items from API, using default menu')\n          setMenuItems([])\n        }\n      } catch (error) {\n        // في حالة خطأ في الشبكة، استخدم القائمة الافتراضية\n        console.warn('Error fetching menu items, using default menu:', error)\n        setMenuItems([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchMenuItems()\n  }, [])\n\n  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون\n  const getNavItemsFromDB = () => {\n    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة\n    const mainItems = menuItems.filter(item => !item.parent_id && item.is_active)\n\n    return mainItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // تحديد الأيقونة\n      let icon = <LinkIcon className=\"h-4 w-4\" />\n      if (item.icon) {\n        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون\n        switch (item.icon) {\n          case 'Home':\n            icon = <Home className=\"h-4 w-4\" />\n            break\n          case 'ShoppingBag':\n            icon = <ShoppingBag className=\"h-4 w-4\" />\n            break\n          case 'Palette':\n            icon = <Palette className=\"h-4 w-4\" />\n            break\n          case 'Search':\n            icon = <Search className=\"h-4 w-4\" />\n            break\n          case 'Info':\n            icon = <Info className=\"h-4 w-4\" />\n            break\n          case 'Phone':\n            icon = <Phone className=\"h-4 w-4\" />\n            break\n          case 'Grid3X3':\n            icon = <Grid3X3 className=\"h-4 w-4\" />\n            break\n          case 'ExternalLink':\n            icon = <ExternalLink className=\"h-4 w-4\" />\n            break\n          case 'FileText':\n            icon = <FileText className=\"h-4 w-4\" />\n            break\n          default:\n            icon = <LinkIcon className=\"h-4 w-4\" />\n        }\n      }\n\n      // البحث عن القوائم الفرعية لهذا العنصر\n      const subItems = menuItems\n        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)\n        .map(subItem => {\n          let subLabel = subItem.title_ar\n          if (locale === 'en' && subItem.title_en) {\n            subLabel = subItem.title_en\n          } else if (locale === 'fr' && subItem.title_fr) {\n            subLabel = subItem.title_fr\n          }\n\n          let subHref = subItem.target_value\n          if (subItem.target_type === 'page') {\n            subHref = `/pages/${subItem.target_value}`\n          }\n\n          return {\n            href: subHref,\n            label: subLabel,\n            target_type: subItem.target_type\n          }\n        })\n\n      return {\n        href,\n        label,\n        icon,\n        target_type: item.target_type,\n        subItems: subItems.length > 0 ? subItems : undefined\n      }\n    })\n  }\n\n  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)\n  const defaultNavItems: NavItem[] = [\n    {\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ]\n\n  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية\n  const navItems = loading ? defaultNavItems : (menuItems.length > 0 ? getNavItemsFromDB() : defaultNavItems)\n\n  // استخدام عناصر القائمة فقط (بدون لوحة التحكم في القائمة الرئيسية)\n  const { user, profile } = useAuth()\n  const allNavItems = navItems\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-3\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-3 hover:opacity-80 transition-all duration-300 group\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center gap-1\">\n            {allNavItems.map((item) => {\n              // تحديد ما إذا كان الرابط خارجي\n              const isExternal = item.target_type === 'external'\n              const hasSubItems = item.subItems && item.subItems.length > 0\n\n              // إذا كان العنصر له قوائم فرعية\n              if (hasSubItems) {\n                return (\n                  <div key={item.href} className=\"relative group\">\n                    <button\n                      className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                      }`}\n                    >\n                      <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                        {item.icon}\n                      </span>\n                      <span className=\"text-sm font-medium\">\n                        {item.label}\n                      </span>\n                      <ChevronDown className=\"h-3 w-3 transition-transform group-hover:rotate-180\" />\n                    </button>\n\n                    {/* القائمة الفرعية */}\n                    <div className=\"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"py-2\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  </div>\n                )\n              }\n\n              // العناصر العادية بدون قوائم فرعية\n              const linkProps = isExternal\n                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                : { href: item.href }\n\n              return (\n                <Link\n                  key={item.href}\n                  {...linkProps}\n                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                    isActive(item.href)\n                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                  }`}\n                >\n                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                    {item.icon}\n                  </span>\n                  <span className=\"text-sm font-medium\">\n                    {item.label}\n                  </span>\n                  {isExternal && (\n                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                  )}\n                  {isActive(item.href) && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden lg:flex items-center gap-2\">\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartItemsCount > 99 ? '99+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n          </div>\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartItemsCount > 9 ? '9+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4\">\n            <nav className=\"flex flex-col gap-1 mb-6\">\n              {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const hasSubItems = item.subItems && item.subItems.length > 0\n\n                // إذا كان العنصر له قوائم فرعية\n                if (hasSubItems) {\n                  return (\n                    <div key={item.href} className=\"mx-2\">\n                      <div\n                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${\n                          isActive(item.href)\n                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                        }`}\n                        style={{\n                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                          animationDuration: '0.3s',\n                          animationTimingFunction: 'ease-out',\n                          animationFillMode: 'forwards',\n                          animationDelay: `${index * 50}ms`\n                        }}\n                      >\n                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                          {item.icon}\n                        </span>\n                        <span className=\"text-sm flex-1\">\n                          {item.label}\n                        </span>\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </div>\n\n                      {/* القوائم الفرعية للموبايل */}\n                      <div className=\"ml-6 mt-2 space-y-1\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              onClick={() => setIsMobileMenuOpen(false)}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  )\n                }\n\n                // العناصر العادية بدون قوائم فرعية\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.href}\n                    {...linkProps}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"text-sm\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n            </nav>\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;;AAyDO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,oBAAoB;QACtB;+BAAG;QAAC;KAAS;IAEb,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,uDAAuD;YACvD,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,kBAAkB,UAAU,MAAM;YAElC,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;YAC1E,iBAAiB,cAAc,MAAM;QACvC;+BAAG,EAAE;IAEL,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;uDAAiB;oBACrB,IAAI;wBACF,WAAW;wBACX,6CAA6C;wBAC7C,MAAM,WAAW,MAAM,MAAM;wBAE7B,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,aAAa,KAAK,SAAS,IAAI,EAAE;wBACnC,OAAO;4BACL,6CAA6C;4BAC7C,QAAQ,IAAI,CAAC;4BACb,aAAa,EAAE;wBACjB;oBACF,EAAE,OAAO,OAAO;wBACd,mDAAmD;wBACnD,QAAQ,IAAI,CAAC,kDAAkD;wBAC/D,aAAa,EAAE;oBACjB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;+BAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB;QACxB,+DAA+D;QAC/D,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS;QAE5E,OAAO,UAAU,GAAG,CAAC,CAAA;YACnB,mCAAmC;YACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;YACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBACpC,QAAQ,KAAK,QAAQ;YACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,QAAQ,KAAK,QAAQ;YACvB;YAEA,6BAA6B;YAC7B,IAAI,OAAO,KAAK,YAAY;YAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;gBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;YACtC;YAEA,iBAAiB;YACjB,IAAI,qBAAO,6LAAC,qMAAA,CAAA,OAAQ;gBAAC,WAAU;;;;;;YAC/B,IAAI,KAAK,IAAI,EAAE;gBACb,+CAA+C;gBAC/C,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,qBAAO,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC9B;oBACF,KAAK;wBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACzB;oBACF,KAAK;wBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACxB;oBACF,KAAK;wBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF,KAAK;wBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF;wBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;gBAC/B;YACF;YAEA,uCAAuC;YACvC,MAAM,WAAW,UACd,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE,IAAI,QAAQ,SAAS,EACpE,GAAG,CAAC,CAAA;gBACH,IAAI,WAAW,QAAQ,QAAQ;gBAC/B,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBACvC,WAAW,QAAQ,QAAQ;gBAC7B,OAAO,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBAC9C,WAAW,QAAQ,QAAQ;gBAC7B;gBAEA,IAAI,UAAU,QAAQ,YAAY;gBAClC,IAAI,QAAQ,WAAW,KAAK,QAAQ;oBAClC,UAAU,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;gBAC5C;gBAEA,OAAO;oBACL,MAAM;oBACN,OAAO;oBACP,aAAa,QAAQ,WAAW;gBAClC;YACF;YAEF,OAAO;gBACL;gBACA;gBACA;gBACA,aAAa,KAAK,WAAW;gBAC7B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;YAC7C;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,uBAAuB;YAChC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,yBAAyB;YAClC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,wDAAwD;IACxD,MAAM,WAAW,UAAU,kBAAmB,UAAU,MAAM,GAAG,IAAI,sBAAsB;IAE3F,mEAAmE;IACnE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc;IAEpB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgE;;;;;;sDAGhF,6LAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,gCAAgC;gCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;gCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;gCAE5D,gCAAgC;gCAChC,IAAI,aAAa;oCACf,qBACE,6LAAC;wCAAoB,WAAU;;0DAC7B,6LAAC;gDACC,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;kEAEF,6LAAC;wDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;kEAChH,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;kEAEb,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAIzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DANrB,QAAQ,IAAI;;;;;oDAUvB;;;;;;;;;;;;uCAtCI,KAAK,IAAI;;;;;gCA2CvB;gCAEA,mCAAmC;gCACnC,MAAM,YAAY,aACd;oCAAE,MAAM,KAAK,IAAI;oCAAE,QAAQ;oCAAU,KAAK;gCAAsB,IAChE;oCAAE,MAAM,KAAK,IAAI;gCAAC;gCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEF,GAAG,SAAS;oCACb,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;sDAEF,6LAAC;4CAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sDAChH,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;wCAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAEzB,SAAS,KAAK,IAAI,mBACjB,6LAAC;4CAAI,WAAU;;;;;;;mCAlBZ,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOvC,6LAAC,8JAAA,CAAA,uBAAoB;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;sDACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAW,OAAO;8CAC5D,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOrC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDACC,WAAW,CAAC,qFAAqF,EAC/F,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,OAAO;wDACL,eAAe,mBAAmB,qBAAqB;wDACvD,mBAAmB;wDACnB,yBAAyB;wDACzB,mBAAmB;wDACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;oDACnC;;sEAEA,6LAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;sEAC3F,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DAPrB,QAAQ,IAAI;;;;;oDAWvB;;;;;;;2CA7CM,KAAK,IAAI;;;;;oCAiDvB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,0FAA0F,EACpG,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,6LAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,IAAI;;;;;gCA2BpB;;;;;;0CAIF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;0DACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAW,OAAO;0DAC5D,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GArgBgB;;QACQ,iIAAA,CAAA,iBAAc;QACnB,qIAAA,CAAA,cAAW;QA4KF,kIAAA,CAAA,UAAO;;;KA9KnB;uCAugBD", "debugId": null}}, {"offset": {"line": 2700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 2949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'\nimport { Button } from './button'\n\nexport interface Toast {\n  id: string\n  title?: string\n  description: string\n  type: 'success' | 'error' | 'warning' | 'info'\n  duration?: number\n}\n\ninterface ToastProps {\n  toast: Toast\n  onRemove: (id: string) => void\n}\n\nexport function ToastComponent({ toast, onRemove }: ToastProps) {\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onRemove(toast.id)\n    }, toast.duration || 5000)\n\n    return () => clearTimeout(timer)\n  }, [toast.id, toast.duration, onRemove])\n\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-600\" />\n      case 'error':\n        return <AlertCircle className=\"h-5 w-5 text-red-600\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-600\" />\n    }\n  }\n\n  const getBgColor = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200'\n      case 'error':\n        return 'bg-red-50 border-red-200'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200'\n      case 'info':\n        return 'bg-blue-50 border-blue-200'\n    }\n  }\n\n  return (\n    <div className={`rounded-lg border p-4 shadow-lg ${getBgColor()} animate-in slide-in-from-right-full`}>\n      <div className=\"flex items-start gap-3\">\n        {getIcon()}\n        <div className=\"flex-1\">\n          {toast.title && (\n            <h4 className=\"font-medium text-gray-900 arabic-text\">{toast.title}</h4>\n          )}\n          <p className=\"text-sm text-gray-700 arabic-text\">{toast.description}</p>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-6 w-6 p-0\"\n          onClick={() => onRemove(toast.id)}\n        >\n          <X className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    </div>\n  )\n}\n\ninterface ToastContainerProps {\n  toasts: Toast[]\n  onRemove: (id: string) => void\n}\n\nexport function ToastContainer({ toasts, onRemove }: ToastContainerProps) {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {toasts.map((toast) => (\n        <ToastComponent key={toast.id} toast={toast} onRemove={onRemove} />\n      ))}\n    </div>\n  )\n}\n\n// Hook لإدارة التوست\nexport function useToast() {\n  const [toasts, setToasts] = useState<Toast[]>([])\n\n  const addToast = (toast: Omit<Toast, 'id'>) => {\n    const id = Date.now().toString()\n    setToasts(prev => [...prev, { ...toast, id }])\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const success = (description: string, title?: string) => {\n    addToast({ type: 'success', description, title })\n  }\n\n  const error = (description: string, title?: string) => {\n    addToast({ type: 'error', description, title })\n  }\n\n  const warning = (description: string, title?: string) => {\n    addToast({ type: 'warning', description, title })\n  }\n\n  const info = (description: string, title?: string) => {\n    addToast({ type: 'info', description, title })\n  }\n\n  return {\n    toasts,\n    addToast,\n    removeToast,\n    success,\n    error,\n    warning,\n    info\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAmBO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAc;;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,SAAS,MAAM,EAAE;gBACnB;iDAAG,MAAM,QAAQ,IAAI;YAErB;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC,MAAM,EAAE;QAAE,MAAM,QAAQ;QAAE;KAAS;IAEvC,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,gCAAgC,EAAE,aAAa,oCAAoC,CAAC;kBACnG,cAAA,6LAAC;YAAI,WAAU;;gBACZ;8BACD,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,KAAK,kBACV,6LAAC;4BAAG,WAAU;sCAAyC,MAAM,KAAK;;;;;;sCAEpE,6LAAC;4BAAE,WAAU;sCAAqC,MAAM,WAAW;;;;;;;;;;;;8BAErE,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,SAAS,MAAM,EAAE;8BAEhC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB;GAxDgB;KAAA;AA+DT,SAAS,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAuB;IACtE,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAA8B,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIrC;MARgB;AAWT,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC;QAChB,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ;QAC9B,UAAU,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,GAAG,KAAK;oBAAE;gBAAG;aAAE;IAC/C;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,UAAU,CAAC,aAAqB;QACpC,SAAS;YAAE,MAAM;YAAW;YAAa;QAAM;IACjD;IAEA,MAAM,QAAQ,CAAC,aAAqB;QAClC,SAAS;YAAE,MAAM;YAAS;YAAa;QAAM;IAC/C;IAEA,MAAM,UAAU,CAAC,aAAqB;QACpC,SAAS;YAAE,MAAM;YAAW;YAAa;QAAM;IACjD;IAEA,MAAM,OAAO,CAAC,aAAqB;QACjC,SAAS;YAAE,MAAM;YAAQ;YAAa;QAAM;IAC9C;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IArCgB", "debugId": null}}, {"offset": {"line": 3181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/OrderStatusBadge.tsx"], "sourcesContent": ["import { Badge } from '@/components/ui/badge'\nimport { \n  Clock, \n  CheckCircle, \n  Package, \n  Truck, \n  XCircle,\n  AlertCircle\n} from 'lucide-react'\n\ninterface OrderStatusBadgeProps {\n  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'\n  showIcon?: boolean\n}\n\nexport function OrderStatusBadge({ status, showIcon = true }: OrderStatusBadgeProps) {\n  const getStatusConfig = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return {\n          text: 'في الانتظار',\n          className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',\n          icon: Clock\n        }\n      case 'confirmed':\n        return {\n          text: 'مؤكد',\n          className: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',\n          icon: CheckCircle\n        }\n      case 'in_production':\n        return {\n          text: 'قيد الإنتاج',\n          className: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',\n          icon: Package\n        }\n      case 'shipped':\n        return {\n          text: 'تم الشحن',\n          className: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',\n          icon: Truck\n        }\n      case 'delivered':\n        return {\n          text: 'تم التسليم',\n          className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',\n          icon: CheckCircle\n        }\n      case 'cancelled':\n        return {\n          text: 'ملغي',\n          className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',\n          icon: XCircle\n        }\n      default:\n        return {\n          text: status,\n          className: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',\n          icon: AlertCircle\n        }\n    }\n  }\n\n  const config = getStatusConfig(status)\n  const Icon = config.icon\n\n  return (\n    <Badge className={config.className}>\n      {showIcon && <Icon className=\"h-3 w-3 mr-1\" />}\n      <span className=\"arabic-text\">{config.text}</span>\n    </Badge>\n  )\n}\n\ninterface PaymentStatusBadgeProps {\n  status: 'pending' | 'paid' | 'failed' | 'refunded'\n  showIcon?: boolean\n}\n\nexport function PaymentStatusBadge({ status, showIcon = true }: PaymentStatusBadgeProps) {\n  const getPaymentStatusConfig = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return {\n          text: 'في الانتظار',\n          className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',\n          icon: Clock\n        }\n      case 'paid':\n        return {\n          text: 'مدفوع',\n          className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',\n          icon: CheckCircle\n        }\n      case 'failed':\n        return {\n          text: 'فشل',\n          className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',\n          icon: XCircle\n        }\n      case 'refunded':\n        return {\n          text: 'مسترد',\n          className: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',\n          icon: AlertCircle\n        }\n      default:\n        return {\n          text: status,\n          className: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',\n          icon: AlertCircle\n        }\n    }\n  }\n\n  const config = getPaymentStatusConfig(status)\n  const Icon = config.icon\n\n  return (\n    <Badge className={config.className}>\n      {showIcon && <Icon className=\"h-3 w-3 mr-1\" />}\n      <span className=\"arabic-text\">{config.text}</span>\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAcO,SAAS,iBAAiB,EAAE,MAAM,EAAE,WAAW,IAAI,EAAyB;IACjF,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,uMAAA,CAAA,QAAK;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,8NAAA,CAAA,cAAW;gBACnB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,2MAAA,CAAA,UAAO;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,uMAAA,CAAA,QAAK;gBACb;YACF,KAAK;g<PERSON><PERSON>,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,8NAAA,CAAA,cAAW;gBACnB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,+MAAA,CAAA,UAAO;gBACf;YACF;gBACE,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,uNAAA,CAAA,cAAW;gBACnB;QACJ;IACF;IAEA,MAAM,SAAS,gBAAgB;IAC/B,MAAM,OAAO,OAAO,IAAI;IAExB,qBACE,6LAAC,oIAAA,CAAA,QAAK;QAAC,WAAW,OAAO,SAAS;;YAC/B,0BAAY,6LAAC;gBAAK,WAAU;;;;;;0BAC7B,6LAAC;gBAAK,WAAU;0BAAe,OAAO,IAAI;;;;;;;;;;;;AAGhD;KAzDgB;AAgET,SAAS,mBAAmB,EAAE,MAAM,EAAE,WAAW,IAAI,EAA2B;IACrF,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,uMAAA,CAAA,QAAK;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,8NAAA,CAAA,cAAW;gBACnB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,+MAAA,CAAA,UAAO;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,uNAAA,CAAA,cAAW;gBACnB;YACF;gBACE,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,MAAM,uNAAA,CAAA,cAAW;gBACnB;QACJ;IACF;IAEA,MAAM,SAAS,uBAAuB;IACtC,MAAM,OAAO,OAAO,IAAI;IAExB,qBACE,6LAAC,oIAAA,CAAA,QAAK;QAAC,WAAW,OAAO,SAAS;;YAC/B,0BAAY,6LAAC;gBAAK,WAAU;;;;;;0BAC7B,6LAAC;gBAAK,WAAU;0BAAe,OAAO,IAAI;;;;;;;;;;;;AAGhD;MA7CgB", "debugId": null}}, {"offset": {"line": 3346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 3575, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/OrderDetailsDialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\n\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Textarea } from '@/components/ui/textarea'\nimport { OrderStatusBadge, PaymentStatusBadge } from './OrderStatusBadge'\nimport { useToast } from '@/components/ui/toast'\nimport { \n  User, \n  Mail, \n  Phone, \n  MapPin, \n  Package, \n  CreditCard,\n\n  Save,\n  X\n} from 'lucide-react'\n\ninterface OrderItem {\n  id: string\n  product_name: string\n  product_image: string\n  category: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  customizations?: {\n    color?: string\n    size?: string\n    embroidery?: string\n    special_requests?: string\n  }\n}\n\ninterface Order {\n  id: string\n  order_number: string\n  customer_name: string\n  customer_email: string\n  customer_phone?: string\n  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'\n  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'\n  payment_method?: string\n  items: OrderItem[]\n  subtotal: number\n  tax: number\n  shipping_cost: number\n  total: number\n  shipping_address: {\n    street: string\n    city: string\n    state: string\n    postal_code: string\n    country: string\n  }\n  tracking_number?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  delivery_date?: string\n  school_name?: string\n}\n\ninterface OrderDetailsDialogProps {\n  order: Order | null\n  isOpen: boolean\n  onClose: () => void\n  onUpdate: (orderId: string, updates: any) => Promise<void>\n}\n\nexport function OrderDetailsDialog({ order, isOpen, onClose, onUpdate }: OrderDetailsDialogProps) {\n  const toast = useToast()\n  const [isUpdating, setIsUpdating] = useState(false)\n  const [newStatus, setNewStatus] = useState('')\n  const [newPaymentStatus, setNewPaymentStatus] = useState('')\n  const [newNotes, setNewNotes] = useState('')\n\n  if (!order) return null\n\n  const handleUpdate = async () => {\n    try {\n      setIsUpdating(true)\n      \n      const updates: any = {}\n      if (newStatus && newStatus !== order.status) {\n        updates.status = newStatus\n      }\n      if (newPaymentStatus && newPaymentStatus !== order.payment_status) {\n        updates.payment_status = newPaymentStatus\n      }\n      if (newNotes !== order.notes) {\n        updates.notes = newNotes\n      }\n\n      if (Object.keys(updates).length > 0) {\n        await onUpdate(order.id, updates)\n        toast.success('تم تحديث الطلب بنجاح')\n        onClose()\n      } else {\n        toast.info('لا توجد تغييرات للحفظ')\n      }\n    } catch (error) {\n      toast.error('فشل في تحديث الطلب')\n    } finally {\n      setIsUpdating(false)\n    }\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"arabic-text\">\n            تفاصيل الطلب {order.order_number}\n          </DialogTitle>\n        </DialogHeader>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Customer Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text flex items-center gap-2\">\n                <User className=\"h-5 w-5\" />\n                معلومات العميل\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center gap-3\">\n                <User className=\"h-4 w-4 text-gray-500\" />\n                <span className=\"font-medium arabic-text\">{order.customer_name}</span>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <Mail className=\"h-4 w-4 text-gray-500\" />\n                <span className=\"text-sm\">{order.customer_email}</span>\n              </div>\n              {order.customer_phone && (\n                <div className=\"flex items-center gap-3\">\n                  <Phone className=\"h-4 w-4 text-gray-500\" />\n                  <span className=\"text-sm\">{order.customer_phone}</span>\n                </div>\n              )}\n              {order.school_name && (\n                <div className=\"flex items-center gap-3\">\n                  <Package className=\"h-4 w-4 text-gray-500\" />\n                  <span className=\"text-sm arabic-text\">{order.school_name}</span>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Order Status */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text flex items-center gap-2\">\n                <Package className=\"h-5 w-5\" />\n                حالة الطلب\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium arabic-text\">حالة الطلب:</span>\n                <OrderStatusBadge status={order.status} />\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium arabic-text\">حالة الدفع:</span>\n                <PaymentStatusBadge status={order.payment_status} />\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium arabic-text\">طريقة الدفع:</span>\n                <span className=\"text-sm\">{order.payment_method || 'غير محدد'}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium arabic-text\">تاريخ الطلب:</span>\n                <span className=\"text-sm\">{new Date(order.created_at).toLocaleDateString('ar-SA')}</span>\n              </div>\n              {order.tracking_number && (\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium arabic-text\">رقم التتبع:</span>\n                  <span className=\"text-sm font-mono\">{order.tracking_number}</span>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Shipping Address */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text flex items-center gap-2\">\n                <MapPin className=\"h-5 w-5\" />\n                عنوان التوصيل\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-sm space-y-1\">\n                <div>{order.shipping_address.street}</div>\n                <div>{order.shipping_address.city}, {order.shipping_address.state}</div>\n                <div>{order.shipping_address.postal_code}</div>\n                <div>{order.shipping_address.country}</div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Order Summary */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text flex items-center gap-2\">\n                <CreditCard className=\"h-5 w-5\" />\n                ملخص الطلب\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"arabic-text\">المجموع الفرعي:</span>\n                <span>{order.subtotal.toFixed(2)} Dhs</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"arabic-text\">الضريبة:</span>\n                <span>{order.tax.toFixed(2)} Dhs</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"arabic-text\">الشحن:</span>\n                <span>{order.shipping_cost.toFixed(2)} Dhs</span>\n              </div>\n              <div className=\"border-t pt-3\">\n                <div className=\"flex justify-between font-bold\">\n                  <span className=\"arabic-text\">الإجمالي:</span>\n                  <span>{order.total.toFixed(2)} Dhs</span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Order Items */}\n        <Card className=\"mt-6\">\n          <CardHeader>\n            <CardTitle className=\"arabic-text\">عناصر الطلب</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {order.items.map((item) => (\n                <div key={item.id} className=\"flex items-center gap-4 p-4 border rounded-lg\">\n                  <div className=\"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center\">\n                    <Package className=\"h-8 w-8 text-gray-500\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-medium arabic-text\">{item.product_name}</h4>\n                    <p className=\"text-sm text-gray-600\">الكمية: {item.quantity}</p>\n                    {item.customizations && (\n                      <div className=\"text-xs text-gray-500 mt-1\">\n                        {item.customizations.color && <span>اللون: {item.customizations.color} </span>}\n                        {item.customizations.size && <span>المقاس: {item.customizations.size} </span>}\n                        {item.customizations.embroidery && <span>التطريز: {item.customizations.embroidery}</span>}\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"font-medium\">{item.total_price.toFixed(2)} Dhs</div>\n                    <div className=\"text-sm text-gray-500\">{item.unit_price.toFixed(2)} Dhs / قطعة</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Update Form */}\n        <Card className=\"mt-6\">\n          <CardHeader>\n            <CardTitle className=\"arabic-text\">تحديث الطلب</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"text-sm font-medium arabic-text\">حالة الطلب</label>\n                <Select value={newStatus || order.status} onValueChange={setNewStatus}>\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"pending\">في الانتظار</SelectItem>\n                    <SelectItem value=\"confirmed\">مؤكد</SelectItem>\n                    <SelectItem value=\"in_production\">قيد الإنتاج</SelectItem>\n                    <SelectItem value=\"shipped\">تم الشحن</SelectItem>\n                    <SelectItem value=\"delivered\">تم التسليم</SelectItem>\n                    <SelectItem value=\"cancelled\">ملغي</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div>\n                <label className=\"text-sm font-medium arabic-text\">حالة الدفع</label>\n                <Select value={newPaymentStatus || order.payment_status} onValueChange={setNewPaymentStatus}>\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"pending\">في الانتظار</SelectItem>\n                    <SelectItem value=\"paid\">مدفوع</SelectItem>\n                    <SelectItem value=\"failed\">فشل</SelectItem>\n                    <SelectItem value=\"refunded\">مسترد</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"text-sm font-medium arabic-text\">ملاحظات</label>\n              <Textarea\n                value={newNotes || order.notes || ''}\n                onChange={(e) => setNewNotes(e.target.value)}\n                placeholder=\"إضافة ملاحظات...\"\n                className=\"arabic-text\"\n              />\n            </div>\n\n            <div className=\"flex gap-3 pt-4\">\n              <Button onClick={handleUpdate} disabled={isUpdating}>\n                <Save className=\"h-4 w-4 mr-2\" />\n                {isUpdating ? 'جاري الحفظ...' : 'حفظ التغييرات'}\n              </Button>\n              <Button variant=\"outline\" onClick={onClose}>\n                <X className=\"h-4 w-4 mr-2\" />\n                إلغاء\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAXA;;;;;;;;;;AA2EO,SAAS,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAA2B;;IAC9F,MAAM,QAAQ,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,eAAe;QACnB,IAAI;YACF,cAAc;YAEd,MAAM,UAAe,CAAC;YACtB,IAAI,aAAa,cAAc,MAAM,MAAM,EAAE;gBAC3C,QAAQ,MAAM,GAAG;YACnB;YACA,IAAI,oBAAoB,qBAAqB,MAAM,cAAc,EAAE;gBACjE,QAAQ,cAAc,GAAG;YAC3B;YACA,IAAI,aAAa,MAAM,KAAK,EAAE;gBAC5B,QAAQ,KAAK,GAAG;YAClB;YAEA,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;gBACnC,MAAM,SAAS,MAAM,EAAE,EAAE;gBACzB,MAAM,OAAO,CAAC;gBACd;YACF,OAAO;gBACL,MAAM,IAAI,CAAC;YACb;QACF,EAAE,OAAO,OAAO;YACd,MAAM,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;;4BAAc;4BACrB,MAAM,YAAY;;;;;;;;;;;;8BAIpC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIhC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAA2B,MAAM,aAAa;;;;;;;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAW,MAAM,cAAc;;;;;;;;;;;;wCAEhD,MAAM,cAAc,kBACnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAW,MAAM,cAAc;;;;;;;;;;;;wCAGlD,MAAM,WAAW,kBAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;oDAAK,WAAU;8DAAuB,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;sCAOhE,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAInC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,6LAAC,kJAAA,CAAA,mBAAgB;oDAAC,QAAQ,MAAM,MAAM;;;;;;;;;;;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,6LAAC,kJAAA,CAAA,qBAAkB;oDAAC,QAAQ,MAAM,cAAc;;;;;;;;;;;;sDAElD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,6LAAC;oDAAK,WAAU;8DAAW,MAAM,cAAc,IAAI;;;;;;;;;;;;sDAErD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,6LAAC;oDAAK,WAAU;8DAAW,IAAI,KAAK,MAAM,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;wCAE1E,MAAM,eAAe,kBACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,6LAAC;oDAAK,WAAU;8DAAqB,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIlC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK,MAAM,gBAAgB,CAAC,MAAM;;;;;;0DACnC,6LAAC;;oDAAK,MAAM,gBAAgB,CAAC,IAAI;oDAAC;oDAAG,MAAM,gBAAgB,CAAC,KAAK;;;;;;;0DACjE,6LAAC;0DAAK,MAAM,gBAAgB,CAAC,WAAW;;;;;;0DACxC,6LAAC;0DAAK,MAAM,gBAAgB,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;sCAM1C,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAItC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;;wDAAM,MAAM,QAAQ,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAEnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;;wDAAM,MAAM,GAAG,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAE9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;;wDAAM,MAAM,aAAa,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAExC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,6LAAC;;4DAAM,MAAM,KAAK,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQxC,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAc;;;;;;;;;;;sCAErC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2B,KAAK,YAAY;;;;;;kEAC1D,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAS,KAAK,QAAQ;;;;;;;oDAC1D,KAAK,cAAc,kBAClB,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,cAAc,CAAC,KAAK,kBAAI,6LAAC;;oEAAK;oEAAQ,KAAK,cAAc,CAAC,KAAK;oEAAC;;;;;;;4DACrE,KAAK,cAAc,CAAC,IAAI,kBAAI,6LAAC;;oEAAK;oEAAS,KAAK,cAAc,CAAC,IAAI;oEAAC;;;;;;;4DACpE,KAAK,cAAc,CAAC,UAAU,kBAAI,6LAAC;;oEAAK;oEAAU,KAAK,cAAc,CAAC,UAAU;;;;;;;;;;;;;;;;;;;0DAIvF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAAe,KAAK,WAAW,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAC1D,6LAAC;wDAAI,WAAU;;4DAAyB,KAAK,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;uCAjB7D,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;8BA0BzB,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAc;;;;;;;;;;;sCAErC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkC;;;;;;8DACnD,6LAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO,aAAa,MAAM,MAAM;oDAAE,eAAe;;sEACvD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;8EAC5B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAgB;;;;;;8EAClC,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;8EAC5B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;;;;;;;;;;;;;;;;;;;sDAKpC,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkC;;;;;;8DACnD,6LAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO,oBAAoB,MAAM,cAAc;oDAAE,eAAe;;sEACtE,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;8EAC5B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAO;;;;;;8EACzB,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMrC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkC;;;;;;sDACnD,6LAAC,uIAAA,CAAA,WAAQ;4CACP,OAAO,YAAY,MAAM,KAAK,IAAI;4CAClC,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAc,UAAU;;8DACvC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,aAAa,kBAAkB;;;;;;;sDAElC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;;8DACjC,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GArQgB;;QACA,oIAAA,CAAA,WAAQ;;;KADR", "debugId": null}}, {"offset": {"line": 4675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/dashboard/admin/orders/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Navigation } from '@/components/Navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\n\nimport { Input } from '@/components/ui/input'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { \n  DropdownMenu, \n  DropdownMenuContent, \n  DropdownMenuItem, \n  DropdownMenuTrigger,\n  DropdownMenuSeparator\n} from '@/components/ui/dropdown-menu'\nimport { useToast } from '@/components/ui/toast'\nimport { OrderStatusBadge, PaymentStatusBadge } from '@/components/admin/OrderStatusBadge'\nimport { OrderDetailsDialog } from '@/components/admin/OrderDetailsDialog'\nimport Link from 'next/link'\nimport { \n  Package, \n  Search, \n  Filter, \n  MoreHorizontal,\n  Eye,\n  Edit,\n  Trash2,\n  <PERSON>L<PERSON><PERSON>,\n  Download,\n  Refresh<PERSON><PERSON>,\n  TrendingUp,\n  Clock,\n  CheckCircle,\n  XCircle,\n  Truck,\n  DollarSign\n} from 'lucide-react'\n\n// أنواع البيانات\ninterface Order {\n  id: string\n  order_number: string\n  customer_name: string\n  customer_email: string\n  customer_phone?: string\n  status: 'pending' | 'confirmed' | 'in_production' | 'shipped' | 'delivered' | 'cancelled'\n  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'\n  payment_method?: string\n  items: any[]\n  subtotal: number\n  tax: number\n  shipping_cost: number\n  total: number\n  shipping_address: {\n    street: string\n    city: string\n    state: string\n    postal_code: string\n    country: string\n  }\n  tracking_number?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  delivery_date?: string\n  school_name?: string\n}\n\ninterface OrderStats {\n  total: number\n  pending: number\n  confirmed: number\n  in_production: number\n  shipped: number\n  delivered: number\n  cancelled: number\n  total_revenue: number\n  pending_payments: number\n}\n\nexport default function OrdersManagement() {\n  const { user, profile } = useAuth()\n  const toast = useToast()\n  const [orders, setOrders] = useState<Order[]>([])\n  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])\n  const [stats, setStats] = useState<OrderStats | null>(null)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [statusFilter, setStatusFilter] = useState<string>('all')\n  const [paymentFilter, setPaymentFilter] = useState<string>('all')\n  const [sortBy, setSortBy] = useState<string>('created_at')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n  const [loading, setLoading] = useState(true)\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)\n  const [showOrderDetails, setShowOrderDetails] = useState(false)\n\n  // التحقق من صلاحيات الأدمن\n  useEffect(() => {\n    if (!user || !profile || profile.role !== 'admin') {\n      window.location.href = '/dashboard'\n      return\n    }\n  }, [user, profile])\n\n  // جلب الطلبات\n  const fetchOrders = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/orders')\n      const data = await response.json()\n      \n      if (response.ok) {\n        setOrders(data.orders || [])\n        setStats(data.stats || null)\n      } else {\n        toast.error(data.error || 'فشل في جلب الطلبات')\n      }\n    } catch (error) {\n      console.error('Error fetching orders:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchOrders()\n  }, [])\n\n  // دالة تحديث الطلب\n  const handleUpdateOrder = async (orderId: string, updates: any) => {\n    try {\n      const response = await fetch(`/api/orders/${orderId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(updates),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        // تحديث قائمة الطلبات\n        setOrders(prevOrders =>\n          prevOrders.map(order =>\n            order.id === orderId ? { ...order, ...updates } : order\n          )\n        )\n        toast.success('تم تحديث الطلب بنجاح')\n      } else {\n        toast.error(data.error || 'فشل في تحديث الطلب')\n      }\n    } catch (error) {\n      console.error('Error updating order:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n    }\n  }\n\n  // دالة حذف الطلب\n  const handleDeleteOrder = async (orderId: string) => {\n    if (!confirm('هل أنت متأكد من حذف هذا الطلب؟')) {\n      return\n    }\n\n    try {\n      const response = await fetch(`/api/orders/${orderId}`, {\n        method: 'DELETE',\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        setOrders(prevOrders => prevOrders.filter(order => order.id !== orderId))\n        toast.success('تم حذف الطلب بنجاح')\n      } else {\n        toast.error(data.error || 'فشل في حذف الطلب')\n      }\n    } catch (error) {\n      console.error('Error deleting order:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n    }\n  }\n\n  // دالة عرض تفاصيل الطلب\n  const handleViewOrder = (order: Order) => {\n    setSelectedOrder(order)\n    setShowOrderDetails(true)\n  }\n\n  // تطبيق الفلاتر والبحث\n  useEffect(() => {\n    let filtered = [...orders]\n\n    // البحث\n    if (searchTerm) {\n      filtered = filtered.filter(order =>\n        order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        order.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        order.customer_email.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // فلترة حسب الحالة\n    if (statusFilter !== 'all') {\n      filtered = filtered.filter(order => order.status === statusFilter)\n    }\n\n    // فلترة حسب حالة الدفع\n    if (paymentFilter !== 'all') {\n      filtered = filtered.filter(order => order.payment_status === paymentFilter)\n    }\n\n    // ترتيب النتائج\n    filtered.sort((a, b) => {\n      let aValue: any = a[sortBy as keyof Order]\n      let bValue: any = b[sortBy as keyof Order]\n\n      if (sortBy === 'created_at') {\n        aValue = new Date(aValue).getTime()\n        bValue = new Date(bValue).getTime()\n      }\n\n      if (sortOrder === 'desc') {\n        return bValue > aValue ? 1 : -1\n      } else {\n        return aValue > bValue ? 1 : -1\n      }\n    })\n\n    setFilteredOrders(filtered)\n  }, [orders, searchTerm, statusFilter, paymentFilter, sortBy, sortOrder])\n\n\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n        <Navigation />\n        <main className=\"container mx-auto px-4 py-8\">\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n          </div>\n        </main>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <Navigation />\n      \n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center gap-4 mb-4\">\n            <Button variant=\"outline\" size=\"sm\" asChild>\n              <Link href=\"/dashboard/admin\">\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                العودة للوحة التحكم\n              </Link>\n            </Button>\n          </div>\n          \n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text\">\n                إدارة الطلبات 📦\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n                إدارة ومتابعة جميع طلبات العملاء\n              </p>\n            </div>\n            <div className=\"flex gap-3\">\n              <Button variant=\"outline\" onClick={fetchOrders}>\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                تحديث\n              </Button>\n              <Button variant=\"outline\">\n                <Download className=\"h-4 w-4 mr-2\" />\n                تصدير\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Statistics Cards */}\n        {stats && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                      إجمالي الطلبات\n                    </p>\n                    <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                      {stats.total}\n                    </p>\n                  </div>\n                  <Package className=\"h-8 w-8 text-blue-600\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                      الطلبات المعلقة\n                    </p>\n                    <p className=\"text-2xl font-bold text-yellow-600\">\n                      {stats.pending}\n                    </p>\n                  </div>\n                  <Clock className=\"h-8 w-8 text-yellow-600\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                      الطلبات المكتملة\n                    </p>\n                    <p className=\"text-2xl font-bold text-green-600\">\n                      {stats.delivered}\n                    </p>\n                  </div>\n                  <CheckCircle className=\"h-8 w-8 text-green-600\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                      إجمالي المبيعات\n                    </p>\n                    <p className=\"text-2xl font-bold text-blue-600\">\n                      {stats.total_revenue.toFixed(2)} Dhs\n                    </p>\n                  </div>\n                  <DollarSign className=\"h-8 w-8 text-blue-600\" />\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Filters and Search */}\n        <Card className=\"mb-6\">\n          <CardHeader>\n            <CardTitle className=\"arabic-text\">البحث والفلترة</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div>\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                  <Input\n                    placeholder=\"البحث في الطلبات...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"pl-10 arabic-text\"\n                  />\n                </div>\n              </div>\n\n              <Select value={statusFilter} onValueChange={setStatusFilter}>\n                <SelectTrigger className=\"arabic-text\">\n                  <SelectValue placeholder=\"حالة الطلب\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">جميع الحالات</SelectItem>\n                  <SelectItem value=\"pending\">في الانتظار</SelectItem>\n                  <SelectItem value=\"confirmed\">مؤكد</SelectItem>\n                  <SelectItem value=\"in_production\">قيد الإنتاج</SelectItem>\n                  <SelectItem value=\"shipped\">تم الشحن</SelectItem>\n                  <SelectItem value=\"delivered\">تم التسليم</SelectItem>\n                  <SelectItem value=\"cancelled\">ملغي</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select value={paymentFilter} onValueChange={setPaymentFilter}>\n                <SelectTrigger className=\"arabic-text\">\n                  <SelectValue placeholder=\"حالة الدفع\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">جميع حالات الدفع</SelectItem>\n                  <SelectItem value=\"pending\">في الانتظار</SelectItem>\n                  <SelectItem value=\"paid\">مدفوع</SelectItem>\n                  <SelectItem value=\"failed\">فشل</SelectItem>\n                  <SelectItem value=\"refunded\">مسترد</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {\n                const [field, order] = value.split('-')\n                setSortBy(field)\n                setSortOrder(order as 'asc' | 'desc')\n              }}>\n                <SelectTrigger className=\"arabic-text\">\n                  <SelectValue placeholder=\"ترتيب حسب\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"created_at-desc\">الأحدث أولاً</SelectItem>\n                  <SelectItem value=\"created_at-asc\">الأقدم أولاً</SelectItem>\n                  <SelectItem value=\"total-desc\">المبلغ (الأعلى أولاً)</SelectItem>\n                  <SelectItem value=\"total-asc\">المبلغ (الأقل أولاً)</SelectItem>\n                  <SelectItem value=\"customer_name-asc\">اسم العميل (أ-ي)</SelectItem>\n                  <SelectItem value=\"customer_name-desc\">اسم العميل (ي-أ)</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Orders Table */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <CardTitle className=\"arabic-text\">قائمة الطلبات</CardTitle>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400 arabic-text\">\n                {filteredOrders.length} من {orders.length} طلب\n              </div>\n            </div>\n          </CardHeader>\n          <CardContent>\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b\">\n                    <th className=\"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text\">\n                      رقم الطلب\n                    </th>\n                    <th className=\"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text\">\n                      العميل\n                    </th>\n                    <th className=\"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text\">\n                      المدرسة\n                    </th>\n                    <th className=\"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text\">\n                      حالة الطلب\n                    </th>\n                    <th className=\"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text\">\n                      حالة الدفع\n                    </th>\n                    <th className=\"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text\">\n                      المبلغ\n                    </th>\n                    <th className=\"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text\">\n                      التاريخ\n                    </th>\n                    <th className=\"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text\">\n                      الإجراءات\n                    </th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredOrders.map((order) => (\n                    <tr key={order.id} className=\"border-b hover:bg-gray-50 dark:hover:bg-gray-800\">\n                      <td className=\"p-4\">\n                        <div className=\"font-medium text-blue-600 arabic-text\">\n                          {order.order_number}\n                        </div>\n                        <div className=\"text-sm text-gray-500 arabic-text\">\n                          {order.items.length} عنصر\n                        </div>\n                      </td>\n                      <td className=\"p-4\">\n                        <div className=\"font-medium text-gray-900 dark:text-white arabic-text\">\n                          {order.customer_name}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {order.customer_email}\n                        </div>\n                      </td>\n                      <td className=\"p-4\">\n                        <div className=\"text-sm text-gray-900 dark:text-white arabic-text\">\n                          {order.school_name || 'غير محدد'}\n                        </div>\n                      </td>\n                      <td className=\"p-4\">\n                        <OrderStatusBadge status={order.status} />\n                      </td>\n                      <td className=\"p-4\">\n                        <PaymentStatusBadge status={order.payment_status} />\n                      </td>\n                      <td className=\"p-4\">\n                        <div className=\"font-medium text-gray-900 dark:text-white\">\n                          {order.total.toFixed(2)} Dhs\n                        </div>\n                      </td>\n                      <td className=\"p-4\">\n                        <div className=\"text-sm text-gray-900 dark:text-white\">\n                          {new Date(order.created_at).toLocaleDateString('ar-SA')}\n                        </div>\n                      </td>\n                      <td className=\"p-4\">\n                        <DropdownMenu>\n                          <DropdownMenuTrigger asChild>\n                            <Button variant=\"ghost\" size=\"sm\">\n                              <MoreHorizontal className=\"h-4 w-4\" />\n                            </Button>\n                          </DropdownMenuTrigger>\n                          <DropdownMenuContent align=\"end\">\n                            <DropdownMenuItem onClick={() => handleViewOrder(order)}>\n                              <Eye className=\"h-4 w-4 mr-2\" />\n                              عرض التفاصيل\n                            </DropdownMenuItem>\n                            <DropdownMenuItem onClick={() => handleViewOrder(order)}>\n                              <Edit className=\"h-4 w-4 mr-2\" />\n                              تحديث الحالة\n                            </DropdownMenuItem>\n                            <DropdownMenuItem>\n                              <Truck className=\"h-4 w-4 mr-2\" />\n                              تتبع الشحنة\n                            </DropdownMenuItem>\n                            <DropdownMenuSeparator />\n                            <DropdownMenuItem\n                              className=\"text-red-600\"\n                              onClick={() => handleDeleteOrder(order.id)}\n                            >\n                              <Trash2 className=\"h-4 w-4 mr-2\" />\n                              حذف\n                            </DropdownMenuItem>\n                          </DropdownMenuContent>\n                        </DropdownMenu>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n\n              {filteredOrders.length === 0 && (\n                <div className=\"text-center py-8\">\n                  <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-500 arabic-text\">لا توجد طلبات</p>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Order Details Dialog */}\n        <OrderDetailsDialog\n          order={selectedOrder}\n          isOpen={showOrderDetails}\n          onClose={() => {\n            setShowOrderDetails(false)\n            setSelectedOrder(null)\n          }}\n          onUpdate={handleUpdateOrder}\n        />\n\n\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AArBA;;;;;;;;;;;;;;AAkFe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,QAAQ,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,QAAQ,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;gBACjD,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACF;QACF;qCAAG;QAAC;QAAM;KAAQ;IAElB,cAAc;IACd,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU,KAAK,MAAM,IAAI,EAAE;gBAC3B,SAAS,KAAK,KAAK,IAAI;YACzB,OAAO;gBACL,MAAM,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,oBAAoB,OAAO,SAAiB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,sBAAsB;gBACtB,UAAU,CAAA,aACR,WAAW,GAAG,CAAC,CAAA,QACb,MAAM,EAAE,KAAK,UAAU;4BAAE,GAAG,KAAK;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAGtD,MAAM,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,KAAK,CAAC;QACd;IACF;IAEA,iBAAiB;IACjB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,mCAAmC;YAC9C;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE;gBACrD,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU,CAAA,aAAc,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;gBAChE,MAAM,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,KAAK,CAAC;QACd;IACF;IAEA,wBAAwB;IACxB,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,oBAAoB;IACtB;IAEA,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW;mBAAI;aAAO;YAE1B,QAAQ;YACR,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;kDAAC,CAAA,QACzB,MAAM,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAChE,MAAM,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,MAAM,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;YAEtE;YAEA,mBAAmB;YACnB,IAAI,iBAAiB,OAAO;gBAC1B,WAAW,SAAS,MAAM;kDAAC,CAAA,QAAS,MAAM,MAAM,KAAK;;YACvD;YAEA,uBAAuB;YACvB,IAAI,kBAAkB,OAAO;gBAC3B,WAAW,SAAS,MAAM;kDAAC,CAAA,QAAS,MAAM,cAAc,KAAK;;YAC/D;YAEA,gBAAgB;YAChB,SAAS,IAAI;8CAAC,CAAC,GAAG;oBAChB,IAAI,SAAc,CAAC,CAAC,OAAsB;oBAC1C,IAAI,SAAc,CAAC,CAAC,OAAsB;oBAE1C,IAAI,WAAW,cAAc;wBAC3B,SAAS,IAAI,KAAK,QAAQ,OAAO;wBACjC,SAAS,IAAI,KAAK,QAAQ,OAAO;oBACnC;oBAEA,IAAI,cAAc,QAAQ;wBACxB,OAAO,SAAS,SAAS,IAAI,CAAC;oBAChC,OAAO;wBACL,OAAO,SAAS,SAAS,IAAI,CAAC;oBAChC;gBACF;;YAEA,kBAAkB;QACpB;qCAAG;QAAC;QAAQ;QAAY;QAAc;QAAe;QAAQ;KAAU;IAIvE,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;8BACX,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,OAAO;8CACzC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;0CAM5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA+D;;;;;;0DAG7E,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;kDAInE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS;;kEACjC,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ5C,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAmE;;;;;;kEAGhF,6LAAC;wDAAE,WAAU;kEACV,MAAM,KAAK;;;;;;;;;;;;0DAGhB,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKzB,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAmE;;;;;;kEAGhF,6LAAC;wDAAE,WAAU;kEACV,MAAM,OAAO;;;;;;;;;;;;0DAGlB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKvB,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAmE;;;;;;kEAGhF,6LAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;0DAGpB,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAK7B,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAmE;;;;;;kEAGhF,6LAAC;wDAAE,WAAU;;4DACV,MAAM,aAAa,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAGpC,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAc;;;;;;;;;;;0CAErC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;;;;;;sDAKhB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAc,eAAe;;8DAC1C,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;sEAC9B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAgB;;;;;;sEAClC,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;sEAC9B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;;;;;;;;;;;;;sDAIlC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAe,eAAe;;8DAC3C,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;;;;;;;;;;;;;sDAIjC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO,GAAG,OAAO,CAAC,EAAE,WAAW;4CAAE,eAAe,CAAC;gDACvD,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,KAAK,CAAC;gDACnC,UAAU;gDACV,aAAa;4CACf;;8DACE,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAkB;;;;;;sEACpC,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAiB;;;;;;sEACnC,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa;;;;;;sEAC/B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;sEAC9B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAoB;;;;;;sEACtC,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAc;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;gDACZ,eAAe,MAAM;gDAAC;gDAAK,OAAO,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAIhD,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;8DACC,cAAA,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;0EAAuE;;;;;;0EAGrF,6LAAC;gEAAG,WAAU;0EAAuE;;;;;;0EAGrF,6LAAC;gEAAG,WAAU;0EAAuE;;;;;;0EAGrF,6LAAC;gEAAG,WAAU;0EAAuE;;;;;;0EAGrF,6LAAC;gEAAG,WAAU;0EAAuE;;;;;;0EAGrF,6LAAC;gEAAG,WAAU;0EAAuE;;;;;;0EAGrF,6LAAC;gEAAG,WAAU;0EAAuE;;;;;;0EAGrF,6LAAC;gEAAG,WAAU;0EAAuE;;;;;;;;;;;;;;;;;8DAKzF,6LAAC;8DACE,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;4DAAkB,WAAU;;8EAC3B,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;sFACZ,MAAM,YAAY;;;;;;sFAErB,6LAAC;4EAAI,WAAU;;gFACZ,MAAM,KAAK,CAAC,MAAM;gFAAC;;;;;;;;;;;;;8EAGxB,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;sFACZ,MAAM,aAAa;;;;;;sFAEtB,6LAAC;4EAAI,WAAU;sFACZ,MAAM,cAAc;;;;;;;;;;;;8EAGzB,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;kFACZ,MAAM,WAAW,IAAI;;;;;;;;;;;8EAG1B,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC,kJAAA,CAAA,mBAAgB;wEAAC,QAAQ,MAAM,MAAM;;;;;;;;;;;8EAExC,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC,kJAAA,CAAA,qBAAkB;wEAAC,QAAQ,MAAM,cAAc;;;;;;;;;;;8EAElD,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;4EACZ,MAAM,KAAK,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;8EAG5B,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;kFACZ,IAAI,KAAK,MAAM,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;8EAGnD,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC,+IAAA,CAAA,eAAY;;0FACX,6LAAC,+IAAA,CAAA,sBAAmB;gFAAC,OAAO;0FAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAQ,MAAK;8FAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gFAAC,OAAM;;kGACzB,6LAAC,+IAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,gBAAgB;;0GAC/C,6LAAC,mMAAA,CAAA,MAAG;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;kGAGlC,6LAAC,+IAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,gBAAgB;;0GAC/C,6LAAC,8MAAA,CAAA,OAAI;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;kGAGnC,6LAAC,+IAAA,CAAA,mBAAgB;;0GACf,6LAAC,uMAAA,CAAA,QAAK;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;kGAGpC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kGACtB,6LAAC,+IAAA,CAAA,mBAAgB;wFACf,WAAU;wFACV,SAAS,IAAM,kBAAkB,MAAM,EAAE;;0GAEzC,6LAAC,6MAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;;;;;;;;;;;;;;;;;;;2DA/DpC,MAAM,EAAE;;;;;;;;;;;;;;;;wCA0EtB,eAAe,MAAM,KAAK,mBACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,6LAAC,oJAAA,CAAA,qBAAkB;wBACjB,OAAO;wBACP,QAAQ;wBACR,SAAS;4BACP,oBAAoB;4BACpB,iBAAiB;wBACnB;wBACA,UAAU;;;;;;;;;;;;;;;;;;AAOpB;GApewB;;QACI,kIAAA,CAAA,UAAO;QACnB,oIAAA,CAAA,WAAQ;;;KAFA", "debugId": null}}]}