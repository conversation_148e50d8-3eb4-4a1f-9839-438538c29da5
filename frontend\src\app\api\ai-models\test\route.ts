import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'
import { ModelTestRequest, ModelTestResponse } from '@/types/ai-models'

// POST - اختبار نموذج ذكاء اصطناعي
export async function POST(request: NextRequest) {
  try {
    const body: ModelTestRequest = await request.json()
    const { modelId, subModelId, prompt, settings } = body

    // التحقق من البيانات المطلوبة
    if (!modelId || !prompt) {
      return NextResponse.json(
        { error: 'معرف النموذج والنص المطلوب مطلوبان' },
        { status: 400 }
      )
    }

    // جلب النموذج
    const models = MockDataManager.getAIModels()
    const model = models.find(m => m.id === modelId)

    if (!model) {
      return NextResponse.json(
        { error: 'النموذج غير موجود' },
        { status: 404 }
      )
    }

    if (!model.isActive) {
      return NextResponse.json(
        { error: 'النموذج غير نشط' },
        { status: 400 }
      )
    }

    // التحقق من النموذج الفرعي إذا تم تحديده
    let subModel = null
    if (subModelId) {
      subModel = model.subModels.find(sm => sm.id === subModelId)
      if (!subModel) {
        return NextResponse.json(
          { error: 'النموذج الفرعي غير موجود' },
          { status: 404 }
        )
      }
      if (!subModel.isActive) {
        return NextResponse.json(
          { error: 'النموذج الفرعي غير نشط' },
          { status: 400 }
        )
      }
    }

    // محاكاة اختبار النموذج
    const startTime = Date.now()
    
    // محاكاة وقت الاستجابة بناءً على نوع النموذج
    const baseResponseTime = model.provider === 'openai' ? 1000 : 
                           model.provider === 'anthropic' ? 1500 :
                           model.provider === 'google' ? 800 : 1200
    
    const responseTime = baseResponseTime + Math.floor(Math.random() * 1000)
    
    // محاكاة نسبة نجاح عالية (95%)
    const success = Math.random() > 0.05
    
    // محاكاة عدد الرموز المستخدمة
    const promptTokens = Math.ceil(prompt.length / 4) // تقدير تقريبي
    const completionTokens = success ? Math.floor(Math.random() * 500) + 50 : 0
    const totalTokens = promptTokens + completionTokens
    
    // حساب التكلفة التقديرية
    const pricing = subModel?.pricing || { inputTokens: 0.001, outputTokens: 0.002, currency: 'USD' }
    const cost = success ? 
      (promptTokens / 1000 * pricing.inputTokens) + (completionTokens / 1000 * pricing.outputTokens) : 
      0

    // إنشاء الاستجابة
    const testResponse: ModelTestResponse = {
      success,
      responseTime,
      tokensUsed: totalTokens,
      cost,
      metadata: {
        model: model.name,
        subModel: subModel?.name,
        provider: model.provider,
        promptTokens,
        completionTokens,
        settings: { ...model.settings, ...settings }
      }
    }

    if (success) {
      // محاكاة استجابة النموذج
      const responses = [
        'مرحباً! أنا مساعد ذكي جاهز لمساعدتك في أي استفسار.',
        'شكراً لك على اختبار النموذج. يبدو أن كل شيء يعمل بشكل صحيح.',
        'هذا اختبار ناجح للنموذج. يمكنني مساعدتك في مختلف المهام.',
        'النموذج يعمل بكفاءة عالية ومستعد للاستخدام.',
        'تم اختبار النموذج بنجاح. جودة الاستجابة ممتازة.'
      ]
      
      testResponse.response = responses[Math.floor(Math.random() * responses.length)]
    } else {
      testResponse.error = 'فشل في الاتصال بالنموذج. يرجى التحقق من إعدادات API.'
    }

    // تحديث إحصائيات النموذج
    const modelIndex = models.findIndex(m => m.id === modelId)
    if (modelIndex !== -1) {
      const updatedModel = models[modelIndex]
      
      // تحديث آخر اختبار
      updatedModel.lastTestedAt = new Date().toISOString()
      updatedModel.testResult = {
        success,
        responseTime,
        error: success ? undefined : testResponse.error
      }
      
      // تحديث الحالة
      updatedModel.status = success ? 'active' : 'error'
      
      // تحديث إحصائيات الاستخدام
      if (success) {
        updatedModel.usage.totalRequests += 1
        updatedModel.usage.totalTokens += totalTokens
        updatedModel.usage.totalCost += cost
        updatedModel.usage.lastUsed = new Date().toISOString()
        
        // تحديث متوسط وقت الاستجابة
        const totalResponseTime = updatedModel.usage.averageResponseTime * (updatedModel.usage.totalRequests - 1) + responseTime
        updatedModel.usage.averageResponseTime = Math.round(totalResponseTime / updatedModel.usage.totalRequests)
        
        // تحديث معدل النجاح
        const successfulRequests = Math.round(updatedModel.usage.successRate * (updatedModel.usage.totalRequests - 1) / 100) + 1
        updatedModel.usage.successRate = Math.round((successfulRequests / updatedModel.usage.totalRequests) * 100)
      }
      
      updatedModel.updatedAt = new Date().toISOString()
      models[modelIndex] = updatedModel
      MockDataManager.saveAIModels(models)
    }

    // إضافة نشاط
    const activities = MockDataManager.getModelActivities()
    activities.push({
      id: MockDataManager.generateId(),
      modelId,
      subModelId,
      type: 'test',
      description: `اختبار النموذج: ${success ? 'نجح' : 'فشل'}`,
      details: {
        prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
        settings: { ...model.settings, ...settings }
      },
      timestamp: new Date().toISOString(),
      duration: responseTime,
      tokensUsed: totalTokens,
      cost,
      success,
      errorMessage: success ? undefined : testResponse.error
    })
    MockDataManager.saveModelActivities(activities)

    return NextResponse.json(testResponse)

  } catch (error) {
    console.error('Error testing AI model:', error)
    return NextResponse.json(
      { error: 'خطأ في اختبار النموذج' },
      { status: 500 }
    )
  }
}

// GET - جلب نتائج الاختبارات السابقة
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const modelId = searchParams.get('model_id')
    const limit = parseInt(searchParams.get('limit') || '20')

    // جلب الأنشطة
    let activities = MockDataManager.getModelActivities()

    // فلترة أنشطة الاختبار
    activities = activities.filter(activity => activity.type === 'test')

    // فلترة حسب النموذج إذا تم تحديده
    if (modelId) {
      activities = activities.filter(activity => activity.modelId === modelId)
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // تطبيق الحد الأقصى
    activities = activities.slice(0, limit)

    // إحصائيات الاختبارات
    const allTestActivities = MockDataManager.getModelActivities().filter(a => a.type === 'test')
    const stats = {
      total: allTestActivities.length,
      successful: allTestActivities.filter(a => a.success).length,
      failed: allTestActivities.filter(a => !a.success).length,
      averageResponseTime: allTestActivities.length > 0 
        ? Math.round(allTestActivities.reduce((sum, a) => sum + (a.duration || 0), 0) / allTestActivities.length)
        : 0,
      totalCost: allTestActivities.reduce((sum, a) => sum + (a.cost || 0), 0),
      byModel: allTestActivities.reduce((acc, activity) => {
        const modelId = activity.modelId
        if (!acc[modelId]) {
          acc[modelId] = { total: 0, successful: 0, failed: 0 }
        }
        acc[modelId].total += 1
        if (activity.success) {
          acc[modelId].successful += 1
        } else {
          acc[modelId].failed += 1
        }
        return acc
      }, {} as Record<string, { total: number, successful: number, failed: number }>)
    }

    return NextResponse.json({
      tests: activities,
      stats,
      total: activities.length
    })

  } catch (error) {
    console.error('Error fetching test results:', error)
    return NextResponse.json(
      { error: 'خطأ في جلب نتائج الاختبارات' },
      { status: 500 }
    )
  }
}
