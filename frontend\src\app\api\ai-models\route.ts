import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'
import { AIModel, CreateModelRequest } from '@/types/ai-models'

// GET - جلب جميع نماذج الذكاء الاصطناعي
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const provider = searchParams.get('provider')
    const type = searchParams.get('type')
    const status = searchParams.get('status')
    const includeInactive = searchParams.get('include_inactive') === 'true'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    // جلب البيانات الوهمية
    let models = MockDataManager.getAIModels()

    // تطبيق الفلاتر
    if (provider) {
      models = models.filter(model => model.provider === provider)
    }

    if (type) {
      models = models.filter(model => model.type === type)
    }

    if (status) {
      models = models.filter(model => model.status === status)
    }

    if (!includeInactive) {
      models = models.filter(model => model.isActive)
    }

    // تطبيق التصفح
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedModels = models.slice(startIndex, endIndex)

    // إحصائيات إضافية
    const stats = {
      total: models.length,
      active: models.filter(m => m.isActive).length,
      inactive: models.filter(m => !m.isActive).length,
      byProvider: models.reduce((acc, model) => {
        acc[model.provider] = (acc[model.provider] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      byType: models.reduce((acc, model) => {
        acc[model.type] = (acc[model.type] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    }

    return NextResponse.json({
      models: paginatedModels,
      total: models.length,
      page,
      limit,
      totalPages: Math.ceil(models.length / limit),
      stats
    })

  } catch (error) {
    console.error('Error fetching AI models:', error)
    return NextResponse.json(
      { error: 'خطأ في جلب نماذج الذكاء الاصطناعي' },
      { status: 500 }
    )
  }
}

// POST - إضافة نموذج ذكاء اصطناعي جديد
export async function POST(request: NextRequest) {
  try {
    const body: CreateModelRequest = await request.json()
    const {
      name,
      provider,
      type,
      description,
      apiKey,
      apiEndpoint,
      baseUrl,
      settings,
      selectedModels,
      subModels
    } = body

    // التحقق من البيانات المطلوبة
    if (!provider) {
      return NextResponse.json(
        { error: 'مقدم الخدمة مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود baseUrl
    if (!baseUrl) {
      return NextResponse.json(
        { error: 'Base URL مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من تحديد نماذج
    if (!selectedModels || selectedModels.length === 0) {
      return NextResponse.json(
        { error: 'يجب تحديد نموذج واحد على الأقل' },
        { status: 400 }
      )
    }

    // جلب النماذج الحالية
    const models = MockDataManager.getAIModels()

    // التحقق من عدم تكرار مقدم الخدمة
    const existingModel = models.find(model => model.provider === provider)
    if (existingModel) {
      return NextResponse.json(
        { error: 'مقدم الخدمة موجود بالفعل. يمكنك تحرير الإعدادات الموجودة.' },
        { status: 400 }
      )
    }

    // توليد اسم النموذج من مقدم الخدمة
    const providerNames = {
      'openai': 'OpenAI',
      'anthropic': 'Anthropic Claude',
      'google': 'Google Gemini',
      'meta': 'Meta LLaMA',
      'stability': 'Stability AI',
      'cohere': 'Cohere',
      'huggingface': 'Hugging Face',
      'deepseek': 'DeepSeek'
    }

    const generatedName = name || providerNames[provider] || provider

    // تحديد نوع النموذج من النماذج المحددة
    const modelType = type || determineModelType(selectedModels || [])

    // إنشاء النموذج الجديد
    const newModel: AIModel = {
      id: MockDataManager.generateId(),
      name: generatedName,
      provider,
      type: modelType,
      description: description || `نماذج ${generatedName} للذكاء الاصطناعي`,
      subModels: subModels || [],
      selectedModels: selectedModels || [],
      apiKey,
      baseUrl,
      isActive: true,
      status: 'inactive', // يبدأ غير نشط حتى يتم اختباره
      settings: settings || {
        temperature: 0.7,
        maxTokens: 2048,
        topP: 1,
        frequencyPenalty: 0,
        presencePenalty: 0
      },
      usage: {
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        dailyUsage: [],
        monthlyUsage: [],
        averageResponseTime: 0,
        successRate: 0
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // دالة مساعدة لتحديد نوع النموذج
    function determineModelType(models: string[]): any {
      const hasImageModel = models.some(m =>
        m.includes('dall-e') || m.includes('stable-diffusion') || m.includes('vision')
      )
      const hasAudioModel = models.some(m =>
        m.includes('whisper') || m.includes('tts')
      )
      const hasEmbeddingModel = models.some(m =>
        m.includes('embed')
      )
      const hasCodeModel = models.some(m =>
        m.includes('code') || m.includes('coder')
      )
      const hasMultimodalModel = models.some(m =>
        m.includes('vision') || m.includes('multimodal')
      )

      if (hasMultimodalModel) return 'multimodal'
      if (hasImageModel) return 'image'
      if (hasAudioModel) return 'audio'
      if (hasEmbeddingModel) return 'embedding'
      if (hasCodeModel) return 'code'

      return 'text' // افتراضي
    }

    // حفظ النموذج
    models.push(newModel)
    MockDataManager.saveAIModels(models)

    // إضافة نشاط
    const activities = MockDataManager.getModelActivities()
    activities.push({
      id: MockDataManager.generateId(),
      modelId: newModel.id,
      type: 'config_change',
      description: `تم إضافة مقدم خدمة جديد: ${generatedName}`,
      timestamp: new Date().toISOString(),
      success: true
    })
    MockDataManager.saveModelActivities(activities)

    return NextResponse.json({ 
      message: 'تم إضافة النموذج بنجاح',
      model: newModel 
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating AI model:', error)
    return NextResponse.json(
      { error: 'خطأ في إنشاء النموذج' },
      { status: 500 }
    )
  }
}

// PUT - تحديث إعدادات عامة للنماذج
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, modelIds, settings } = body

    if (!action || !modelIds || !Array.isArray(modelIds)) {
      return NextResponse.json(
        { error: 'الإجراء ومعرفات النماذج مطلوبة' },
        { status: 400 }
      )
    }

    const models = MockDataManager.getAIModels()
    const activities = MockDataManager.getModelActivities()
    let updatedCount = 0

    for (const modelId of modelIds) {
      const modelIndex = models.findIndex(m => m.id === modelId)
      if (modelIndex === -1) continue

      const model = models[modelIndex]
      let description = ''

      switch (action) {
        case 'activate':
          model.isActive = true
          model.status = 'active'
          description = `تم تفعيل النموذج: ${model.name}`
          break
        case 'deactivate':
          model.isActive = false
          model.status = 'inactive'
          description = `تم إلغاء تفعيل النموذج: ${model.name}`
          break
        case 'update_settings':
          if (settings) {
            model.settings = { ...model.settings, ...settings }
            description = `تم تحديث إعدادات النموذج: ${model.name}`
          }
          break
        default:
          continue
      }

      model.updatedAt = new Date().toISOString()
      models[modelIndex] = model
      updatedCount++

      // إضافة نشاط
      activities.push({
        id: MockDataManager.generateId(),
        modelId: model.id,
        type: 'config_change',
        description,
        timestamp: new Date().toISOString(),
        success: true
      })
    }

    // حفظ التغييرات
    MockDataManager.saveAIModels(models)
    MockDataManager.saveModelActivities(activities)

    return NextResponse.json({
      message: `تم تحديث ${updatedCount} نموذج بنجاح`,
      updatedCount
    })

  } catch (error) {
    console.error('Error updating AI models:', error)
    return NextResponse.json(
      { error: 'خطأ في تحديث النماذج' },
      { status: 500 }
    )
  }
}

// DELETE - حذف نماذج متعددة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const modelIds = searchParams.get('ids')?.split(',') || []

    if (modelIds.length === 0) {
      return NextResponse.json(
        { error: 'معرفات النماذج مطلوبة' },
        { status: 400 }
      )
    }

    const models = MockDataManager.getAIModels()
    const activities = MockDataManager.getModelActivities()
    let deletedCount = 0

    // حذف النماذج
    const remainingModels = models.filter(model => {
      if (modelIds.includes(model.id)) {
        // إضافة نشاط الحذف
        activities.push({
          id: MockDataManager.generateId(),
          modelId: model.id,
          type: 'config_change',
          description: `تم حذف النموذج: ${model.name}`,
          timestamp: new Date().toISOString(),
          success: true
        })
        deletedCount++
        return false
      }
      return true
    })

    // حفظ التغييرات
    MockDataManager.saveAIModels(remainingModels)
    MockDataManager.saveModelActivities(activities)

    return NextResponse.json({
      message: `تم حذف ${deletedCount} نموذج بنجاح`,
      deletedCount
    })

  } catch (error) {
    console.error('Error deleting AI models:', error)
    return NextResponse.json(
      { error: 'خطأ في حذف النماذج' },
      { status: 500 }
    )
  }
}
