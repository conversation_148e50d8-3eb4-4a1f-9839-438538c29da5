// أنواع بيانات نماذج الذكاء الاصطناعي

export type AIProvider = 'openai' | 'anthropic' | 'google' | 'meta' | 'stability' | 'cohere' | 'huggingface' | 'deepseek'

export type ModelType = 'text' | 'image' | 'audio' | 'multimodal' | 'code' | 'embedding'

export type ModelStatus = 'active' | 'inactive' | 'error' | 'testing'

export interface ModelPricing {
  inputTokens: number // السعر لكل 1000 token
  outputTokens: number // السعر لكل 1000 token
  currency: string // العملة (USD, EUR, etc.)
  unit: string // وحدة القياس
}

export interface ModelLimits {
  maxTokens: number // الحد الأقصى للرموز
  requestsPerMinute: number // الطلبات في الدقيقة
  requestsPerDay: number // الطلبات في اليوم
  contextWindow: number // نافذة السياق
}

export interface ModelSettings {
  temperature?: number // درجة الحرارة (0-2)
  maxTokens?: number // الحد الأقصى للرموز المولدة
  topP?: number // Top-p sampling (0-1)
  frequencyPenalty?: number // عقوبة التكرار (0-2)
  presencePenalty?: number // عقوبة الحضور (0-2)
  stopSequences?: string[] // تسلسلات التوقف
  systemPrompt?: string // الرسالة النظامية
}

export interface DailyUsage {
  date: string // تاريخ اليوم
  requests: number // عدد الطلبات
  tokens: number // عدد الرموز
  cost: number // التكلفة
  errors: number // عدد الأخطاء
}

export interface UsageStats {
  totalRequests: number // إجمالي الطلبات
  totalTokens: number // إجمالي الرموز
  totalCost: number // إجمالي التكلفة
  lastUsed?: string // آخر استخدام
  dailyUsage: DailyUsage[] // الاستخدام اليومي
  monthlyUsage: DailyUsage[] // الاستخدام الشهري
  averageResponseTime: number // متوسط وقت الاستجابة (ms)
  successRate: number // معدل النجاح (%)
}

export interface AISubModel {
  id: string // معرف النموذج الفرعي
  name: string // اسم النموذج
  modelId: string // معرف النموذج الرئيسي
  description: string // وصف النموذج
  version: string // إصدار النموذج
  capabilities: string[] // القدرات
  pricing: ModelPricing // التسعير
  limits: ModelLimits // الحدود
  isActive: boolean // حالة النشاط
  isDefault: boolean // النموذج الافتراضي
  tags: string[] // العلامات
  releaseDate: string // تاريخ الإصدار
  deprecationDate?: string // تاريخ الإهمال
}

export interface AIModel {
  id: string // معرف النموذج
  name: string // اسم النموذج
  provider: AIProvider // مقدم الخدمة
  type: ModelType // نوع النموذج
  description: string // وصف النموذج
  subModels: AISubModel[] // النماذج الفرعية
  apiKey?: string // مفتاح API
  apiEndpoint?: string // نقطة النهاية المخصصة
  baseUrl?: string // رابط API الأساسي
  isActive: boolean // حالة النشاط
  status: ModelStatus // حالة النموذج
  settings: ModelSettings // إعدادات النموذج
  usage: UsageStats // إحصائيات الاستخدام
  createdAt: string // تاريخ الإنشاء
  updatedAt: string // تاريخ التحديث
  lastTestedAt?: string // آخر اختبار
  testResult?: {
    success: boolean
    responseTime: number
    error?: string
  }
  icon?: string // أيقونة النموذج
  color?: string // لون النموذج
  website?: string // موقع الويب
  documentation?: string // رابط التوثيق
  selectedModels?: string[] // النماذج المحددة للعمل بها
}

export interface ModelActivity {
  id: string // معرف النشاط
  modelId: string // معرف النموذج
  subModelId?: string // معرف النموذج الفرعي
  type: 'request' | 'error' | 'test' | 'config_change' // نوع النشاط
  description: string // وصف النشاط
  details?: Record<string, any> // تفاصيل إضافية
  userId?: string // معرف المستخدم
  timestamp: string // الطابع الزمني
  duration?: number // مدة العملية (ms)
  tokensUsed?: number // الرموز المستخدمة
  cost?: number // التكلفة
  success: boolean // حالة النجاح
  errorMessage?: string // رسالة الخطأ
}

export interface ModelTestRequest {
  modelId: string // معرف النموذج
  subModelId?: string // معرف النموذج الفرعي
  prompt: string // النص المطلوب
  settings?: Partial<ModelSettings> // إعدادات الاختبار
}

export interface ModelTestResponse {
  success: boolean // حالة النجاح
  response?: string // الاستجابة
  responseTime: number // وقت الاستجابة (ms)
  tokensUsed?: number // الرموز المستخدمة
  cost?: number // التكلفة
  error?: string // رسالة الخطأ
  metadata?: Record<string, any> // بيانات إضافية
}

export interface ModelUsageFilter {
  modelId?: string // فلترة حسب النموذج
  provider?: AIProvider // فلترة حسب المقدم
  dateFrom?: string // من تاريخ
  dateTo?: string // إلى تاريخ
  type?: ModelType // نوع النموذج
}

export interface ModelConfiguration {
  id: string // معرف التكوين
  name: string // اسم التكوين
  modelId: string // معرف النموذج
  subModelId?: string // معرف النموذج الفرعي
  settings: ModelSettings // الإعدادات
  isDefault: boolean // التكوين الافتراضي
  createdAt: string // تاريخ الإنشاء
  updatedAt: string // تاريخ التحديث
}

// أنواع للاستجابات
export interface AIModelsResponse {
  models: AIModel[]
  total: number
  page?: number
  limit?: number
}

export interface ModelActivitiesResponse {
  activities: ModelActivity[]
  total: number
  page?: number
  limit?: number
}

export interface ModelUsageResponse {
  usage: UsageStats
  activities: ModelActivity[]
  configurations: ModelConfiguration[]
}

// أنواع للطلبات
export interface CreateModelRequest {
  name: string
  provider: AIProvider
  type: ModelType
  description?: string
  apiKey?: string
  apiEndpoint?: string
  baseUrl?: string
  settings?: ModelSettings
  selectedModels?: string[]
  subModels?: AISubModel[]
}

export interface UpdateModelRequest {
  name?: string
  description?: string
  apiKey?: string
  apiEndpoint?: string
  baseUrl?: string
  isActive?: boolean
  settings?: ModelSettings
  selectedModels?: string[]
  subModels?: AISubModel[]
}

export interface ModelGenerationRequest {
  modelId: string
  subModelId?: string
  prompt: string
  settings?: Partial<ModelSettings>
  context?: string
  userId?: string
}

export interface ModelGenerationResponse {
  success: boolean
  response?: string
  tokensUsed?: number
  cost?: number
  responseTime: number
  error?: string
  metadata?: Record<string, any>
}
