// اختبار الاستيرادات للتأكد من عدم وجود أخطاء

console.log('Testing imports...')

// اختبار أنواع البيانات
try {
  const authTypes = require('./src/types/auth.ts')
  console.log('✅ Auth types imported successfully')
} catch (error) {
  console.log('❌ Auth types import failed:', error.message)
}

try {
  const aiModelsTypes = require('./src/types/ai-models.ts')
  console.log('✅ AI Models types imported successfully')
} catch (error) {
  console.log('❌ AI Models types import failed:', error.message)
}

try {
  const pageBuilderTypes = require('./src/types/page-builder.ts')
  console.log('✅ Page Builder types imported successfully')
} catch (error) {
  console.log('❌ Page Builder types import failed:', error.message)
}

console.log('Import test completed!')
