'use client'

import { useState } from 'react'
import { AIModel } from '@/types/ai-models'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Brain, 
  Settings, 
  TestTube, 
  Activity, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  MoreHorizontal,
  Edit,
  Trash2,
  BarChart3,
  Zap
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

interface AIModelCardProps {
  model: AIModel
  onEdit?: (model: AIModel) => void
  onDelete?: (model: AIModel) => void
  onTest?: (model: AIModel) => void
  onToggleActive?: (model: AIModel, isActive: boolean) => void
  onViewUsage?: (model: AIModel) => void
  onSettings?: (model: AIModel) => void
}

export function AIModelCard({
  model,
  onEdit,
  onDelete,
  onTest,
  onToggleActive,
  onViewUsage,
  onSettings
}: AIModelCardProps) {
  const [isLoading, setIsLoading] = useState(false)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500'
      case 'inactive': return 'bg-gray-500'
      case 'error': return 'bg-red-500'
      case 'testing': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />
      case 'inactive': return <XCircle className="h-4 w-4" />
      case 'error': return <AlertTriangle className="h-4 w-4" />
      case 'testing': return <TestTube className="h-4 w-4" />
      default: return <XCircle className="h-4 w-4" />
    }
  }

  const getProviderColor = (provider: string) => {
    switch (provider) {
      case 'openai': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'anthropic': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'google': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'meta': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
      case 'stability': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'text': return '📝'
      case 'image': return '🖼️'
      case 'audio': return '🎵'
      case 'multimodal': return '🔄'
      case 'code': return '💻'
      case 'embedding': return '🔗'
      default: return '🤖'
    }
  }

  const handleToggleActive = async (checked: boolean) => {
    setIsLoading(true)
    try {
      await onToggleActive?.(model, checked)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTest = async () => {
    setIsLoading(true)
    try {
      await onTest?.(model)
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-MA', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-MA').format(num)
  }

  return (
    <Card className="relative overflow-hidden">
      {/* شريط الحالة */}
      <div className={`absolute top-0 left-0 right-0 h-1 ${getStatusColor(model.status)}`} />
      
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="text-2xl">{model.icon || getTypeIcon(model.type)}</div>
            <div>
              <CardTitle className="text-lg arabic-text">{model.name}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="secondary" className={getProviderColor(model.provider)}>
                  {model.provider}
                </Badge>
                <Badge variant="outline">
                  {getTypeIcon(model.type)} {model.type}
                </Badge>
                <div className="flex items-center gap-1">
                  {getStatusIcon(model.status)}
                  <span className="text-sm text-muted-foreground">{model.status}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={model.isActive}
                      onCheckedChange={handleToggleActive}
                      disabled={isLoading}
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{model.isActive ? 'إلغاء التفعيل' : 'تفعيل'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit?.(model)}>
                  <Edit className="h-4 w-4 mr-2" />
                  تحرير
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onSettings?.(model)}>
                  <Settings className="h-4 w-4 mr-2" />
                  الإعدادات
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleTest} disabled={!model.isActive || isLoading}>
                  <TestTube className="h-4 w-4 mr-2" />
                  اختبار
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onViewUsage?.(model)}>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  الإحصائيات
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onDelete?.(model)}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  حذف
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        
        {model.description && (
          <p className="text-sm text-muted-foreground arabic-text mt-2">
            {model.description}
          </p>
        )}

        {/* Base URL */}
        {model.baseUrl && (
          <div className="mt-2">
            <p className="text-xs text-muted-foreground">
              <span className="font-medium">Base URL:</span> {model.baseUrl}
            </p>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* النماذج المحددة */}
        {model.selectedModels && model.selectedModels.length > 0 && (
          <div>
            <h4 className="text-sm font-medium arabic-text mb-2">النماذج المحددة</h4>
            <div className="flex flex-wrap gap-1">
              {model.selectedModels.slice(0, 3).map((modelName) => (
                <Badge
                  key={modelName}
                  variant="default"
                  className="text-xs"
                >
                  {modelName}
                </Badge>
              ))}
              {model.selectedModels.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{model.selectedModels.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* النماذج الفرعية */}
        {model.subModels.length > 0 && (
          <div>
            <h4 className="text-sm font-medium arabic-text mb-2">النماذج الفرعية النشطة</h4>
            <div className="flex flex-wrap gap-1">
              {model.subModels.slice(0, 3).map((subModel) => (
                <Badge
                  key={subModel.id}
                  variant={subModel.isActive ? "default" : "secondary"}
                  className="text-xs"
                >
                  {subModel.name}
                  {subModel.isDefault && <span className="mr-1">⭐</span>}
                </Badge>
              ))}
              {model.subModels.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{model.subModels.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* إحصائيات الاستخدام */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-blue-500" />
              <span className="text-sm text-muted-foreground">الطلبات</span>
            </div>
            <p className="text-lg font-semibold">
              {formatNumber(model.usage.totalRequests)}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-green-500" />
              <span className="text-sm text-muted-foreground">التكلفة</span>
            </div>
            <p className="text-lg font-semibold">
              {formatCurrency(model.usage.totalCost)}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-orange-500" />
              <span className="text-sm text-muted-foreground">متوسط الاستجابة</span>
            </div>
            <p className="text-lg font-semibold">
              {model.usage.averageResponseTime}ms
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-purple-500" />
              <span className="text-sm text-muted-foreground">معدل النجاح</span>
            </div>
            <p className="text-lg font-semibold">
              {model.usage.successRate}%
            </p>
          </div>
        </div>

        {/* آخر اختبار */}
        {model.lastTestedAt && model.testResult && (
          <div className="border-t pt-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">آخر اختبار</span>
              <div className="flex items-center gap-2">
                {model.testResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  {model.testResult.responseTime}ms
                </span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {new Date(model.lastTestedAt).toLocaleDateString('ar-MA')}
            </p>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="flex gap-2 pt-2">
          <Button 
            size="sm" 
            variant="outline" 
            onClick={handleTest}
            disabled={!model.isActive || isLoading}
            className="flex-1"
          >
            <TestTube className="h-4 w-4 mr-2" />
            اختبار
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => onViewUsage?.(model)}
            className="flex-1"
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            الإحصائيات
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
